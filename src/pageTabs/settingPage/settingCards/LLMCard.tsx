import React, { useEffect, useState } from "react";
import {
  message,
  Form,
  Button,
  Input,
  Select,
  InputNumber,
  Card,
  Spin,
} from "antd";
import { useTranslation } from "react-i18next";
import { useRequest } from "src/hook";
import { useForm } from "antd/lib/form/Form";
import {
  getLLMConfig,
  setLLMConfig,
  testLLM,
  getRequestType,
  getModelType,
} from "src/api";

export const LLMCard = () => {
  const { t } = useTranslation();
  const [form] = useForm();
  const tailLayout = {
    wrapperCol: { offset: 4, span: 10 },
  };

  const [llmConfigs, setLLMConfigs] = useState<{
    advanced: string;
    api_key: string;
    max_token: number;
    model: string;
    model_ali: string;
    request_type: string;
    temperature: number;
    top_p: number;
    url: string;
  }>();
  // 上次设置的内容回显
  const { run: getLLMConfigRun } = useRequest(getLLMConfig, {
    manual: true,
    onSuccess: (res) => {
      let llmConfig = res.datas[0];
      setLLMConfigs(llmConfig);
      form.setFieldsValue({
        request_type: llmConfig.request_type || undefined,
        model_ali: llmConfig.model_ali || undefined,
        model: llmConfig.model,
        url: llmConfig.url,
        api_key: llmConfig.api_key,
        max_token: llmConfig.max_token,
        temperature: llmConfig.temperature,
        top_p: llmConfig.top_p,
      });
    },
  });

  const { run: getRequestTypeRun, data: requestTypeOptions } = useRequest(
    getRequestType,
    {
      manual: true,
      formatResult: (res) =>
        res.datas.data.map((request: string[]) => ({
          label: request[0],
          value: request[0],
        })),
    }
  );
  const { run: getModelTypeRun, data: llmOptions } = useRequest(getModelType, {
    manual: true,
    formatResult: (res) =>
      res.datas.data.map((request: string[]) => ({
        label: request[0],
        value: request[0],
      })),
  });
  // 提交表单
  const { run: setLLMConfigRun ,loading:saveLoading} = useRequest(setLLMConfig, {
    manual: true,
    
  });
  const { run: testLLMRun ,loading:testLoading} = useRequest(testLLM, {

    manual: true,
    
  });

  useEffect(() => {
    getRequestTypeRun({ key: "dd:FLFJ_ai_profile_resultType" });
    getModelTypeRun({ key: "dd:FLFJ_ai_profile_model" });
    getLLMConfigRun();
  }, [getLLMConfigRun, getModelTypeRun, getRequestTypeRun]);

  const onFinish = () => {};

  // 测试链接
  const handleTestConnection = () => {
    form.validateFields().then((values) => {
      testLLMRun(values).then(() => {
        message.success("测试成功");
      });
    });
  };

  // 保存配
  const handleSubmit = () => {
    form.validateFields().then((values) => {
      setLLMConfigRun(values).then(() => {
        message.success("保存成功");
      });
    });
  };
  return (
    <section className="cq-new-card flow-card" id="PasswordCard">
      <div className="cq-card__headerbar">
        <h3 className="cq-card__title">
          {t("systemManagement.system.llm.title")}
        </h3>
      </div>
      <section className="card__content">
          <Form
            form={form}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 10 }}
            onFinish={onFinish}
            autoComplete="off"
          >
            <Form.Item
              label={t("systemManagement.system.llm.requestTye")}
              name="request_type"
              rules={[
                {
                  required: true,
                  message: t(
                    "systemManagement.system.llm.requestTypePlaceholder"
                  ),
                },
              ]}
            >
              <Select options={requestTypeOptions} />
            </Form.Item>

            <Form.Item
              label={t("systemManagement.system.llm.llmSelect")}
              name="model_ali"
              rules={[
                {
                  required: true,
                  message: t(
                    "systemManagement.system.llm.llmSelectPlaceholder"
                  ),
                },
              ]}
            >
              <Select
                placeholder={t(
                  "systemManagement.system.llm.llmSelectPlaceholder"
                )}
                options={llmOptions}
              />
            </Form.Item>

            <Form.Item
              label={t("systemManagement.system.llm.llm")}
              name="model"
            >
              <Input type="text" />
            </Form.Item>

            <Form.Item
              label="url"
              name="url"
              rules={[
                {
                  required: true,
                  message: t("systemManagement.system.llm.urlPlaceholder"),
                },
              ]}
            >
              <Input type="text" />
            </Form.Item>

            <Form.Item
              label="api_key"
              name="api_key"
              rules={[
                {
                  required: true,
                  message: t("systemManagement.system.llm.apiKeyPlaceholder"),
                },
              ]}
            >
              <Input type="text" />
            </Form.Item>

            <Form.Item
              label={t("systemManagement.system.llm.maxToken")}
              name="max_token"
            >
              <Input type="number" />
            </Form.Item>

            <Form.Item label="temperature" name="temperature">
              <InputNumber precision={2} step={0.1} style={{ width: "100%" }} />
            </Form.Item>

            <Form.Item label="top_p" name="top_p">
              <InputNumber precision={2} step={0.1} style={{ width: "100%" }} />
            </Form.Item>

            <Form.Item {...tailLayout}>
              <div
                style={{ display: "flex", justifyContent: "center", gap: 20 }}
              >
                <Button type="primary" onClick={handleSubmit} loading={saveLoading}>
                  {t("systemManagement.system.llm.save")}
                </Button>
                <Button type="primary" onClick={handleTestConnection} loading={testLoading}>
                  {t("systemManagement.system.llm.testConnection")}
                </Button>
              </div>
            </Form.Item>
          </Form>
        </section>
    </section>
  );
};
