import { useSelector, useDispatch } from "src/hook";
import { UIModal } from "src/components";
import { useTranslation } from "react-i18next";
import { hideModal } from "src/store/extraSlice/modalVisibleSlice";
import React, { useEffect } from "react";
import { Form, Input, message } from "antd";
import { getAssetsDataSource } from "src/api/connecitonManageNew";

export default function TableRemarkEditModal(props: {refresh: () => void}) {

  const {visible,extraProps:record} = useSelector(
    (state) => state.modal.TableRemarkEditModal
  );
  const {refresh} = props;

  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [formInstance] = Form.useForm();
  
  useEffect(()=>{
    formInstance.setFieldsValue({
      tableRemark:record.table_comment,
    })
  },[record])
  // 修改表备注
  const handleSubmit = () => {
    formInstance.validateFields().then((values) => {
      dispatch(hideModal("TableRemarkEditModal")); 
      getAssetsDataSource({
        catalog_type: 'table',
        query_type: 'alter_comment',
        table_name: record.table_name,
        schema_name: record.schema_name,
        table_comment:values.tableRemark,
        database:record.database_name,
        connect:record.connect,
      }).then((res) => {
        if(res.error_info){
          message.error(t("db.conn.tableEdit.errorInfo"));
        }else {
          refresh();
          message.success(t("db.conn.tableEdit.successInfo"));
        }
      }).catch(() => {
        message.error(t("db.conn.tableEdit.errorInfo"));
      })
    })
  };


  return (
    <UIModal
      title={t("db.conn.operate.tableRemarkEdit")}
      visible={visible}
      onOk={handleSubmit}
      onCancel={() => {
        dispatch(hideModal("TableRemarkEditModal"));
      }}
      width={500}
    >
      <Form form={formInstance}>
        <Form.Item
          label={t("db.conn.fieldAsset.tableRemark")}
          name="tableRemark"
          rules={[
            { required: true, message: t("db.conn.tableRemark.requireMsg") }, // 必填校验
          ]}
        >
          <Input />
        </Form.Item>
      </Form>
    </UIModal>
  );
}
