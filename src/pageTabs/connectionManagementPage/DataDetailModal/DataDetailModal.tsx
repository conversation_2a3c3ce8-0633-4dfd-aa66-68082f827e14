import { UIModal } from "src/components";
import { useTranslation } from "react-i18next";
import React, { useMemo } from "react";
import { Table } from "antd";
import { useDataSource } from "./index";
import { useDispatch ,useSelector} from "src/hook";
import { hideModal } from "src/store/extraSlice/modalVisibleSlice";
import { getScrollX } from "src/util";

export default function DataDetailModal() {
  const { t } = useTranslation();
  const { tableList, loading } = useDataSource();
  const dispatch = useDispatch();
  const {visible} = useSelector((state) => state.modal.DataDetailModal);

  // 表格列定义
  const columns = useMemo(()=>{
    return Object.keys(tableList[0] || {}).map((key) => ({
      title: key,
      dataIndex: key,
      key,
    }))
  },[tableList]);
  // 表格配置
  const tableProps = {
    dataSource: tableList,
    columns: columns,
    loading,
    rowKey: "id",
    scroll: { x: getScrollX(columns), y: "calc(100vh - 300px)" },
  };

  return (
    <UIModal
      title={t("db.conn.operate.dataDetail")}
      visible={visible}
      width={1200}
      onCancel={() => dispatch(hideModal('DataDetailModal'))}

      footer={null}
    >
      <Table {...tableProps} pagination={false} />
    </UIModal>
  );
}
