import { useEffect ,useState} from "react";
import { useSelector, useRequest } from "src/hook";
import { getAssetsDataSource } from "src/api/connecitonManageNew";
// 获取数据源
export const useDataSource = () => {
  const { run, data, loading } = useRequest(getAssetsDataSource, { manual: true });
  const { visible = false, extraProps: record } = useSelector(
    (state) => state.modal.DataDetailModal || false
  );
  useEffect(() => {
    if (visible) {
      run({
        catalog_type: 'table',
        query_type: 'detail_data',
        database: record?.database_name,
        connect: record?.connect,
        table_name: record?.table_name,
        schema_name: record?.schema_name,
      })
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible,record]);

  return {
    tableList: data?.datas || [],
    total: data?.count?.data?.[0]?.[0] || 0,
    loading
  }
}
