export interface ExampleDataTableItem {
    fieldName: string;
    fieldRemark: string;
    columnType: string;
    fieldLength: number;
    fieldPrecision: number;
}

export interface ColumnInfoExampleSearchParams {

    /**
     * 目录类型
     */
    catalog_type: string;
    /**
     * 列名称
     */
    column_name?: string;
    /**
     * 连接名称
     */
    connect?: string;
    /**
     * database名称
     */
    database?: string;
    /**
     * 查询类型
     */
    query_type: string;
    /**
     * schema
     */
    schema_name?: string;
    /**
     * 表名称
     */
    table_name?: string;
    [property: string]: any;
}