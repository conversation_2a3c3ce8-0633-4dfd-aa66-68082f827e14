import { useEffect } from "react";
import { useSelector ,useRequest} from "src/hook";
import { getAssetsDataSource } from "src/api/connecitonManageNew";


export const useDataSource = () => {
  const {visible,extraProps:record} = useSelector(
    (state) => state.modal.ExampleDataModal || false
  );
  const { run:getExampleData, data, loading } = useRequest(getAssetsDataSource,{manual:true});

  const columns = Object.keys(data?.datas?.[0] || {}).map((key) => ({
    title: key,
    dataIndex: key,
    key,
    width: 150,
  }))
  useEffect(() => {
    if(visible){
      getExampleData({
        catalog_type: 'table',
        query_type: 'column_data',
        database: record.database_name,
        connect: record.connect,
        table_name: record.table_name,
        schema_name: record.schema_name,
        column_name: record.column_name,
      });
    }
  },[visible,record]);
  return {
    tableList: data?.datas || [],
    loading,
    visible,
    columns
  }
}