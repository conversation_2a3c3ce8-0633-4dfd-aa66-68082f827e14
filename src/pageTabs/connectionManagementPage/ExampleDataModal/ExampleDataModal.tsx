import { UIModal } from "src/components";
import { useTranslation } from "react-i18next";
import React from "react";
import { Table } from "antd";
import { useDataSource } from "./index";
import { getScrollX } from "src/util";
import { useDispatch } from "src/hook";
import { hideModal } from "src/store/extraSlice/modalVisibleSlice";
export default function ExampleDataModal() {
  const { t } = useTranslation();
  const { tableList, loading, visible ,columns} = useDataSource();
  const dispatch = useDispatch();

  // 表格配置
  const tableProps = {
    dataSource: tableList,
    columns: columns,
    loading,
    rowKey: "id",
    scroll: { x: getScrollX(columns), y: "calc(100vh - 300px)" },
  };

  return (
    <UIModal
      title={t("db.conn.columnInfo.fieldExample")}
      visible={visible}
      width={800}
      onCancel={() => dispatch(hideModal("ExampleDataModal"))}
      footer={null}
      zIndex={1000}
    >
      <Table {...tableProps} pagination={false}/>
    </UIModal>
  );
}
