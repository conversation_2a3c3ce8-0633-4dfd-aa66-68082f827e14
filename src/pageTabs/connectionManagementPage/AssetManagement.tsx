import React, { useEffect } from "react";
import { TableAsset } from "./TableAsset/TableAsset";
import { FieldAsset } from "./FieldAsset/FieldAsset";
import { Tabs } from "antd";
import { getDmsResourceData } from "src/api/connectionManagement";
import { useTranslation } from "react-i18next";
import {useRequest} from "src/hook";
export default function AssetManagement(props: {
  connectionId: string;
  connectionType: string;
  connectionIdsInClassification: string;
}) {
  const { t } = useTranslation();
  const { connectionId, connectionType, connectionIdsInClassification } = props;
  const { run: getOptions, data: databaseOptions } = useRequest(
    getDmsResourceData,
    {
      manual: true,
      formatResult:(res)=>{
        return res.map((item:any)=>{
          return {
            label:item.dbName,
            value:item.dbName
          }
        })
      }
    },
  );
  useEffect(() => {
    getOptions(connectionId, connectionType);
  }, [connectionId, connectionType]);

  return (
    <Tabs defaultActiveKey="1">
      <Tabs.TabPane tab={t("db.conn.tableAssetsCatalog")} key="1">
        <TableAsset connectionId={connectionId} connectionType={connectionType} databaseOptions={databaseOptions} connectionIdsInClassification={connectionIdsInClassification} />
      </Tabs.TabPane>
      <Tabs.TabPane tab={t("db.conn.fieldAssetsCatalog")} key="2">
        <FieldAsset connectionId={connectionId} connectionType={connectionType} databaseOptions={databaseOptions} connectionIdsInClassification={connectionIdsInClassification}/>
      </Tabs.TabPane>
    </Tabs>
  );
}
