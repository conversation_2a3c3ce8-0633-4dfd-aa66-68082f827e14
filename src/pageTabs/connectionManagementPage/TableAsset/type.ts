// 表格项数据类型
export interface TableItem {
	gmt_create: string,
	creator: string,
	owner: string,
	database_name: string,
	schema_name: string,
	table_name: string,
	database_comment: string,
	src_name: string,
	src_host: string,
	table_content: string,
	src_id: number,
	src_type: number,
	meta_type: number,
	col_count: number,
	gmt_modified: string,
	table_num: number,
	table_comment: string
}

/**
 * 表资产目录查询参数
 */
export interface TableAssetSearchParams {
    /**
     * 目录类型
     */
    catalog_type: string;
    /**
     * 连接
     */
    connect?: string;
    /**
     * database名
     */
    database?: string;
    /**
     * 分页
     */
    limit?: string;
    /**
     * 查询类型
     */
    query_type: string;
    /**
     * schema
     */
    schema_name?: string;
    /**
     * 表备注
     */
    table_comment?: string;
    /**
     * 表名称
     */
    table_name?: string;
    /**
     * 页码
     */
    pageNum?: number;
    /**
     * 每页数量
     */
    pageSize?: number;

    [property: string]: any;
}

// 假设的数据库类型
export type DatabaseType = "MYSQL" | "POSTGRES" | "ORACLE" | "OTHER";