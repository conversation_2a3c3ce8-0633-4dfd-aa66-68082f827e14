import React from "react";
import { Table, Input, Select, Button, Space } from "antd";
import { SearchOutlined, DownloadOutlined, RedoOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import styles from "./index.module.scss";
import ExportModal from "../ExportModal/ExportModal";
import ColumnInforModal from "../ColumnInfoModal/ColumnInforModal";
import DataDetailModal from "../DataDetailModal/DataDetailModal";
import TableRemarkEditModal from "../TableRemarkEditModal";
import { TableItem } from "./type";
import { getScrollX } from "src/util";
import { useTablePagination } from "src/hook";
import { ColumnType } from "antd/es/table"; 
import { useAction, useDataSource, useSelectedRow } from "./index";

export const TableAsset = (props: {
  connectionId: string;
  connectionType: string;
  databaseOptions: any;
  connectionIdsInClassification: string;
}) => {
  const {
    connectionId,
    connectionType,
    databaseOptions,
    connectionIdsInClassification,
  } = props;

  const { t } = useTranslation();
  const {
    handleExport,
    clickColunmInfo,
    clickDataDetail,
    clickTableRemarkEdit,
  } = useAction(connectionIdsInClassification);

  const {
    tableList,
    searchParams,
    handleFilterChange,
    loading,
    handleSelectDbName,
    schemaOptions,
    setSearchParams,
    total,
    refresh
  } = useDataSource(connectionIdsInClassification,connectionId);

  const { selectedRowKeys, setSelectedRowKeys } = useSelectedRow();
  const { pagination, handleChange } = useTablePagination(
    {
      current: 1,
      pageSize: 20,
      showSizeChanger: true,
      showQuickJumper: true,
    },
    searchParams,
    setSearchParams
  );

  // 表格列定义
  const columns: ColumnType<TableItem>[] = [

    {
      title: t("db.conn.tableName"),
      dataIndex: "table_name",
      key: "table_name",
      render: (val: string) => val || "-",
    },
    {
      title: t("db.conn.tableRemark"),
      dataIndex: "table_comment",
      key: "table_comment",
      render: (val: string) => val || "-",
    },
    {
      title: t("db.conn.tableFieldCnt"),
      dataIndex: "col_count",
      key: "col_count",
      render: (val: string) => val || "-",
    },
    {
      title: t("db.conn.tableDataCnt"),
      dataIndex: "table_num",
      key: "table_num",
      render: (val: string) => val || "-",
    },
    {
      title: t("db.conn.tableSchema"),
      dataIndex: "schema_name",
      key: "schema_name",
      render: (val?: string) => val || "-",
    },
    {
      title: t("db.conn.tableDatabase"),
      dataIndex: "database_name",
      key: "database_name",
      render: (val: string) => val || "-",
    },
    {
      title: t("db.conn.operate"),
      key: "action",
      width: 250,
      fixed: "right",
      render: (_: any, record: TableItem) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            onClick={() => clickColunmInfo(record)}
          >
            {t("db.conn.operate.fieldInfo")}
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => clickDataDetail(record)}
          >
            {t("db.conn.operate.dataDetail")}
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => clickTableRemarkEdit(record)}
          >
            {t("db.conn.operate.tableRemarkEdit")}
          </Button>
        </Space>
      ),
    },
  ];

  // 表格配置
  const tableProps = {
    dataSource: tableList,
    columns,
    rowKey: "id",
    loading,
    pagination: {
      ...pagination,
      total,
      showTotal: () => `共 ${total} 条数据`,
    },
    rowSelection: {
      selectedRowKeys,
      onChange: (newSelectedRowKeys: React.Key[]) => {
        setSelectedRowKeys(newSelectedRowKeys as string[]);
      },
    },
    onSelect: (record: TableItem, selected: boolean) => {
      console.log("record", record);
    },
    scroll: { x: getScrollX(columns), y: "calc(100vh - 300px)" },
    onChange: handleChange,
  };
  return (
    <>
      {/* 表头筛选区域及导出 */}
      <div className={styles.assetManagementWrap}>
        <Input
          placeholder={t("db.conn.inputPlaceholder")}
          value={searchParams.keyword}
          onChange={(e) => handleFilterChange("table_name", e.target.value)}
          allowClear
          style={{ width: 350, marginLeft: 8 }}
          suffix={<SearchOutlined />}
        />
        <Select
          placeholder={t("db.conn.selectPlaceholder1")}
          onChange={(val: string) => {
            handleSelectDbName(connectionId, val, connectionType);
          }}
          options={databaseOptions}
          style={{ width: 350, marginLeft: 8 }}
          allowClear
          value={searchParams.database}
        />
        <Select
          placeholder={t("db.conn.selectPlaceholder2")}
          onChange={(val) => handleFilterChange("schema_name", val)}
          options={schemaOptions}
          style={{ width: 350, marginLeft: 8 }}
          allowClear
          value={searchParams.schema_name}
        />
        {/* 刷新按钮 */}
        <Button
          type="primary"
          icon={<RedoOutlined />}
          // loading={loadings[2]}
          onClick={() => refresh()}
        />
        {/* 导出按钮靠右 */}
        <div style={{ marginLeft: "auto", display: "flex", gap: 8 }}>
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleExport}
            disabled={selectedRowKeys.length === 0}
          >
            {t("common.btn.export")}
          </Button>
        </div>
      </div>
      {/* 表格 */}
      <Table {...tableProps} />

      {/* 导出弹窗 */}
      <ExportModal  selectedRowKeys={selectedRowKeys} setSelectedRowKeys={setSelectedRowKeys}/>
      {/* 列信息弹窗 */}
      <ColumnInforModal />
      {/* 数据详情弹窗 */}
      <DataDetailModal/>
      {/* 表备注编辑弹窗 */}
      <TableRemarkEditModal refresh={refresh}/>

    </>
  );
};
