import { renderHook, act } from '@testing-library/react-hooks';
import { useDataSource , useSelectedRow} from './index.ts';
import { getAssetsDataSource, getDmsResourceUnfold } from 'src/api';

// Mock API函数
jest.mock('src/api', () => ({
  getAssetsDataSource: jest.fn(),
  getDmsResourceUnfold: jest.fn(),
}));

describe('useDataSource', () => {
  const mockConnectionId = '123';
  const mockSearchParams = {
    catalog_type: 'table',
    connect: mockConnectionId,
    pageNum: 1,
    pageSize: 10,
  };

  it('初始化默认参数', () => {
    const { result } = renderHook(() => useDataSource(mockConnectionId));
    expect(result.current.searchParams).toEqual({
      catalog_type: 'table',
      connect: mockConnectionId,
      table_name: '',
      schema_name: undefined,
      pageNum: 1,
      pageSize: 10,
    });
  });

  it('处理参数变更', () => {
    const { result } = renderHook(() => useDataSource(mockConnectionId));
    act(() => {
      result.current.handleFilterChange('table_name', 'test_table');
    });
    expect(result.current.searchParams.table_name).toBe('test_table');
    expect(result.current.searchParams.table_comment).toBe('test_table');
  });

  it('触发数据请求', async () => {
    const mockData = { datas: [{ id: 1, name: 'Table1' }], count: { data: [0, 1] } };
    getAssetsDataSource.mockResolvedValue(mockData);

    const { result, waitFor } = renderHook(() => useDataSource(mockConnectionId));
    act(() => {
      result.current.getTableList();
    });

    await waitFor(() => {
      expect(result.current.tableList).toEqual(mockData.datas);
      expect(result.current.total).toBe(1);
    });
  });

  it('获取schema列表', async () => {
    const mockSchemas = [{ schemaName: 'schema1' }, { schemaName: 'schema2' }];
    getDmsResourceUnfold.mockResolvedValue(mockSchemas);

    const { result, waitFor } = renderHook(() => useDataSource(mockConnectionId));
    act(() => {
      result.current.handleSelectDbName(mockConnectionId, 'db1', 'mysql');
    });

    await waitFor(() => {
      expect(result.current.schemaOptions).toEqual([
        { label: 'schema1', value: 'schema1' },
        { label: 'schema2', value: 'schema2' },
      ]);
    });
  });
});

describe('useSelectedRow', () => {
  it('管理选中行状态', () => {
    const { result } = renderHook(() => useSelectedRow());
    expect(result.current.selectedRowKeys).toEqual([]);

    act(() => {
      result.current.setSelectedRowKeys(['row1', 'row2']);
    });
    expect(result.current.selectedRowKeys).toEqual(['row1', 'row2']);
  });
});