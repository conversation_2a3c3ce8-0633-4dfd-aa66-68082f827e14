import { useState, useEffect, useCallback } from "react";
import { TableItem, TableAssetSearchParams } from "./type";
import { showModal } from "src/store/extraSlice/modalVisibleSlice";
import { useDispatch, useRequest } from "src/hook";
import { getAssetsDataSource, checkDataProfiling } from "src/api/connecitonManageNew";
import { getDmsResourceUnfold } from "src/api/connectionManagement";
import { message } from "antd";
import { useTranslation } from "react-i18next";
// 获取数据源
export const useDataSource = (connectionIdInClassification: string, connectionId: string) => {
	const { t } = useTranslation();

	const defaultSearchParams: TableAssetSearchParams = {
		catalog_type: "table",
		query_type: "table",
		connect: connectionIdInClassification,
		table_name: "",
		table_comment: "",
		database: undefined,
		schema_name: undefined,
		pageNum: 1,
		pageSize: 10,
	};
	// 查询参数
	const [searchParams, setSearchParams] =
		useState<TableAssetSearchParams>(defaultSearchParams);

	// 表格数据源
	const [tableList, setTableList] = useState<TableItem[]>([]);


	// 数据总条数
	const [total, setTotal] = useState<number>(0);

	// schema列表
	const [schemaOptions, setSchemaOptions] = useState<any[]>([]);

	// 是否执行过数据探查
	const [isChecked, setIsChecked] = useState<boolean>(false);

	// 获取表格数据
	const { run: getTableList, loading } = useRequest(
		getAssetsDataSource,
		{
			manual: true,
			throttleInterval: 1000,
			throwOnError: true,
			onSuccess: (res) => {
				setTableList(res.datas);
				setTotal(res.count?.total);
			},
			onError() {
				setTableList([]);
				setTotal(0);
			}
		}
	);
	// 获取schema列表
	const { run: getSchemaList } = useRequest(
		getDmsResourceUnfold,
		{
			manual: true,
			formatResult: (res) => {
				return res.map((item: any) => {
					return {
						label: item.schemaName,
						value: item.schemaName,
					}
				})
			},
			onSuccess: (res) => {
				setSchemaOptions(res);
			},
			debounceInterval: 500,
		}
	);

	// 处理参数变化
	const handleFilterChange = useCallback((type: string, value: any) => {
		setSearchParams(prev => {
			if (type === "table_name") {
				return { ...prev, table_name: value, table_comment: value };
			}
			return { ...prev, [type]: value };
		});
	}, [setSearchParams])


	// 选择数据库名称
	const handleSelectDbName = (
		connectionId: number | string,
		dbName: string,
		connectionType: string
	) => {
		if (!dbName) {
			setSchemaOptions([]);
		}
		setSearchParams({
			...searchParams,
			database: dbName ? dbName : undefined,
			schema_name: undefined,
		});
		if (dbName) {
			getSchemaList(connectionId, dbName, connectionType);
		}
	};

	useEffect(() => {
		if (isChecked) {
			let params = {
				...searchParams,
				connect: connectionIdInClassification,
				limit: `${(searchParams.pageNum || 1) - 1}|${searchParams?.pageSize || 10}`,
			}
			// 去掉多余的两个参数，使请求参数中不携带，避免后端报错
			params.pageNum = undefined;
			params.pageSize = undefined;
			getTableList({
				...params,
			});
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [searchParams, isChecked]);

	useEffect(() => {
		// 左侧分级切换，重置状态
		setSearchParams({
			...defaultSearchParams
		});
		setIsChecked(false);
		checkDataProfiling(connectionIdInClassification).then(() => {
			setIsChecked(true);

		}).catch(() => {
			setTableList([]);
			setTotal(0);
			message.error(t("db.conn.explore.msg"));
		})
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [connectionIdInClassification])

	// 刷新
	const refresh = () => {
		if (isChecked) {
			let params = {
				...searchParams,
				connect: connectionIdInClassification,
				limit: `${(searchParams.pageNum || 1) - 1}|${searchParams?.pageSize || 10}`,
			}
			// 去掉多余的两个参数，使请求参数中不携带，避免后端报错
			params.pageNum = undefined;
			params.pageSize = undefined;
			getTableList({
				...params,
			});
		} else {
			message.error(t("db.conn.explore.msg"));

		}
	}
	return {
		tableList: tableList || [],
		total: total || 0,
		searchParams,
		loading,
		schemaOptions,
		refresh,
		handleFilterChange,
		handleSelectDbName,
		setSearchParams,
	};
};

// 操作
export const useAction = (connectionIdInClassification: string) => {

	const dispatch = useDispatch();
	// 导出按钮（mock）
	const handleExport = () => {
		dispatch(showModal("ExportAssetsModal"));
	};

	// 列信息点击
	const clickColunmInfo = (record: TableItem) => {
		dispatch(showModal("ColumnInforModal", { ...record, connect: connectionIdInClassification }));
	};

	// 数据详情点击
	const clickDataDetail = (record: TableItem) => {
		dispatch(showModal("DataDetailModal", { ...record, connect: connectionIdInClassification }));
	};

	// 表备注编辑点击
	const clickTableRemarkEdit = (record: TableItem) => {
		dispatch(showModal("TableRemarkEditModal", { ...record, connect: connectionIdInClassification }));
	};

	return {
		handleExport,
		clickColunmInfo,
		clickDataDetail,
		clickTableRemarkEdit,
	};
};

// 行多选批量操作
export const useSelectedRow = () => {
	const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
	return {
		selectedRowKeys,
		setSelectedRowKeys,
	};
};


