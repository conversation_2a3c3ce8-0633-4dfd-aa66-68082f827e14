import { UIModal } from "src/components";
import React from "react";
import { Form, Input } from "antd";
import { useValidator, useOperation } from ".";
import { useTranslation } from "react-i18next";
export default function ExportModal(props: { selectedRowKeys: string[]; setSelectedRowKeys: (keys: string[]) => void }) {
  const { selectedRowKeys, setSelectedRowKeys } = props;


  const { t } = useTranslation();
  const { fileNameValidator } = useValidator();
  const { handleSubmit, formInstance, visible, handleCancel } = useOperation(selectedRowKeys, setSelectedRowKeys);

  return (
    <UIModal
      title={t("common.btn.export")}
      visible={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      width={500}
    >
      <Form form={formInstance}>
        <Form.Item
          label={t("db.conn.fileName")}
          name="fileName"
          rules={[
            { required: true, message: t("db.conn.fileName.hint") }, // 必填校验
            {
              validator: fileNameValidator,
            },
          ]}
        >
          <Input />
        </Form.Item>
      </Form>
    </UIModal>
  );
}
