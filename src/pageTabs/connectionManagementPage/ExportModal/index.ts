
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch, useRequest } from "src/hook";

import { hideModal } from "src/store/extraSlice/modalVisibleSlice";
import { batchExport, downloadFile } from "src/api/connecitonManageNew";
import { Form, message } from "antd";
// 校验是否是以.csv结尾的文件名称
export const useValidator = () => {
    const { t } = useTranslation();

    const fileNameValidator = async (_: any, value: string) => {
        if (!value) {
            // 如果允许为空，可以不加此判断；如果必填已处理，则此处可只校验格式
            return Promise.resolve();
        }
        // 检查是否以 .csv 结尾（不区分大小写）
        const isCsv = value.trim().toLowerCase().endsWith(".csv");
        if (!isCsv) {
            return Promise.reject(
                new Error(t("db.conn.fileName.hint")) // 自定义提示：文件名必须以 .csv 结尾
            );
        }
        // 校验通过
        return Promise.resolve();
    };
    return {
        fileNameValidator,
    }
}

// 操作
export const useOperation = (selectedRowKeys: string[], setSelectedRowKeys: (keys: string[]) => void) => {

    const { t } = useTranslation();
    const { run: exportRun } = useRequest(batchExport, {
        manual: true,
    })
    const { run: downloadRun } = useRequest(downloadFile, {
        manual: true,
    })
    const dispatch = useDispatch();
    const [formInstance] = Form.useForm();
    const visible = useSelector(
        (state) => state.modal.ExportAssetsModal?.visible || false
    );
    // 确认导出
    const handleSubmit = () => {
        formInstance.validateFields().then((values) => {
            console.log("文件名称", values);
            console.log("选中的handles", selectedRowKeys);
            dispatch(hideModal("ExportAssetsModal"));
            exportRun({
                fileName: values.fileName,
                ids: selectedRowKeys.join('|'),
            }).then(() => {
                return downloadRun(values.fileName)
            }).then(() => {
                message.success(t('db.conn.export.success.mgs'))
                // 导出成功后清空选中的行
                setSelectedRowKeys([])
            }).catch(() => {
                message.error(t('db.conn.export.error.mgs'))
            })
            .finally(() => {
                dispatch(hideModal("ExportAssetsModal"));
            })
        })
    };
    // 取消导出
    const handleCancel = () => {
        dispatch(hideModal("ExportAssetsModal"));
    }
    return {
        handleSubmit,
        handleCancel,
        formInstance,
        visible
    }
}
