
import { getAssetsDataSource } from "src/api/connecitonManageNew";
import { useEffect,useState } from "react";
import { useSelector, useRequest } from "src/hook";
import { message } from "antd";
import { useTranslation } from "react-i18next";

// 获取数据源
export const useDataSource = () => {
    const {t} = useTranslation();
    const [page,setPage] = useState(1);
    const [pageSize,setPageSize] = useState(10);
    const { run, data, loading } = useRequest(getAssetsDataSource, {
        manual: true, onSuccess: (res) => {
            if(res.error_info){
                message.error(t("db.conn.columnInfo.errorInfo"));
            }
        },onError: (err) => {
            message.error(t("db.conn.columnInfo.errorInfo"));
        }
    });
    const { visible = false, extraProps: record } = useSelector(
        (state) => state.modal.ColumnInforModal || false
    );

    const handleChange = (pagination: any) => {
        setPage(pagination.current);
        setPageSize(pagination.pageSize);
    }

    useEffect(() => {
        if (visible) {
            run({
                catalog_type: 'table',
                query_type: 'column',
                database: record.database_name,
                connect: record.connect,
                table_name: record.table_name,
                schema_name: record.schema_name,
                limit: `${page - 1}|${pageSize}`
            });
        }
    }, [visible,page,pageSize]);

    return {
        tableList: data?.datas || [],
        loading,
        total: data?.count?.total || 0,
        page,
        pageSize,
        handleChange
    }
}

