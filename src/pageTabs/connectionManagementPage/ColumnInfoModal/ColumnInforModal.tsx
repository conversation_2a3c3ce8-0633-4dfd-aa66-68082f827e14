import { useSelector, useDispatch } from "src/hook";
import { UIModal } from "src/components";
import { useTranslation } from "react-i18next";
import { hideModal, showModal } from "src/store/extraSlice/modalVisibleSlice";
import React from "react";
import { Table, Space, Button } from "antd";
import ExampleDataModal from "../ExampleDataModal/ExampleDataModal";
import { getScrollX } from "src/util";

import { useDataSource } from "./index";
import { ColumnInfoType } from "./type";

export default function ColumnInforModal() {
  const { visible = false ,extraProps:{connect}} = useSelector(

    (state) => state.modal.ColumnInforModal || false
  );
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const { tableList, loading, page, pageSize, total , handleChange} = useDataSource();

  // 表格列定义
  const columns = [
    {
      title: t("db.conn.columnInfo.fieldName"),
      dataIndex: "column_name",
      key: "column_name",
      width: 150,
    },
    {
      title: t("db.conn.columnInfo.fieldRemark"),
      dataIndex: "column_comment",
      key: "column_comment",
      width: 150,
    },
    {
      title: t("db.conn.columnInfo.fieldType"),
      dataIndex: "data_type",
      key: "data_type",
      width: 150,
      render: (val: number) => val,
    },
    {
      title: t("db.conn.columnInfo.fieldLength"),
      dataIndex: "data_length",
      key: "data_length",
      width: 150,
      render: (val: number) => val,
    },
    {
      title: t("db.conn.columnInfo.fieldPrecision"),
      dataIndex: "data_precision",
      key: "data_precision",
      width: 150,
      render: (val: number) => val,
    },
    {
      title: t("db.conn.operate"),
      key: "action",
      width: 70,
      render: (_: any, record: ColumnInfoType) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            onClick={() => {
              dispatch(showModal("ExampleDataModal",{...record,connect}));
            }}
          >
            {t("db.conn.columnInfo.fieldExample")}
          </Button>
        </Space>
      ),
    },
  ];

  // 表格配置
  const tableProps = {
    dataSource: tableList,
    columns: columns,
    loading,
    rowKey: "id",
    pagination: {
      current: page,
      pageSize: pageSize,
      total: total,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: () => `共 ${total} 条数据`,
    },
    scroll: { x: getScrollX(columns), y: "calc(100vh - 300px)" },
    onChange: handleChange
  };

  return (
    <UIModal
      title={t("db.conn.columnInfo.title")}
      visible={visible}
      width={1200}
      onCancel={() => dispatch(hideModal("ColumnInforModal"))}
      footer={null}
    >
      <Table {...tableProps} />
      <ExampleDataModal/>
    </UIModal>
  );
}
