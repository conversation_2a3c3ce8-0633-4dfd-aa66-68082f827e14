// 列信息查询参数
export interface ColumnInfoSearchParams {
    /**
     * 目录类型
     */
    catalog_type?: string;
    /**
     * 连接connect
     */
    connect?: string;
    /**
     * database名称
     */
    database?: string;
    limit?: number;
    /**
     * 查询类型
     */
    query_type?: string;
    /**
     * schema
     */
    schema_name?: string;
    /**
     * 表名称
     */
    table_name?: string;
    [property: string]: any;
}

// 列信息
export interface ColumnInfoType {
    /**
     * 列名称
     */
    field_name?: string;
    /**
     * 列备注
     */
    field_remark?: string;
    /**
     * 列类型
     */
    field_type?: string;
    /**
     * 列长度
     */
    length?: number;
    /**
     * 列精度
     */
    precision?: number;
    /**
     * 列是否主键
     */
    is_primary_key?: boolean;
    [property: string]: any;
}

    