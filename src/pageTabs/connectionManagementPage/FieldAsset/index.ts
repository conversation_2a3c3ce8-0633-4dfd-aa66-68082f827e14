import { useState, useEffect } from "react";
import { FieldAssetSearchParams } from "./type";
import { useDispatch, useRequest } from "src/hook";
import { showModal } from "src/store/extraSlice/modalVisibleSlice";
import { getAssetsDataSource, checkDataProfiling } from "src/api/connecitonManageNew";
import { getDmsResourceUnfold } from "src/api/connectionManagement";
import { useTranslation } from "react-i18next";
import { message } from "antd";
// 操作
export const useAction = () => {
	const dispatch = useDispatch();
	// 导出按钮（mock）
	const handleExport = () => {
		dispatch(showModal("ExportAssetsModal"));
	};
	return {
		handleExport,
	};
};

// 获取数据源
export const useDataSource = (connectionIdsInClassification: string, connectionId: string) => {

	// 默认查询参数
	const defaultSearchParams: FieldAssetSearchParams = {
		catalog_type: "field",
		query_type: "table",
		table_name: "",
		table_comment: "",
		column_name: "",
		column_comments: "",
		database: undefined,
		schema_name: undefined,
		pageNum: 1,
		pageSize: 10,
		connect: connectionIdsInClassification
	};
	const { t } = useTranslation();

	const [schemaOptions, setSchemaOptions] = useState<any[]>([]);

	// 是否执行过数据探查
	const [isChecked, setIsChecked] = useState<boolean>(false);


	// 表格数据源
	const [tableList, setTableList] = useState([]);

	// 数据总条数
	const [total, setTotal] = useState<number>(0);

	// 查询参数
	const [searchParams, setSearchParams] =
		useState<FieldAssetSearchParams>(defaultSearchParams);
	// 获取表格数据
	const {
		run: getTableList,
		loading,
	} = useRequest(getAssetsDataSource, {
		manual: true,
		throttleInterval: 800,
		onSuccess: (res) => {
			setTableList(res.datas);
			setTotal(res.count?.total);
		}
	});
	// 获取schema列表
	const { run: getSchemaList } = useRequest(
		getDmsResourceUnfold,
		{
			manual: true,
			formatResult: (res) => {
				return res.map((item: any) => {
					return {
						label: item.schemaName,
						value: item.schemaName,
					};
				});
			},
			onSuccess: (res) => {
				setSchemaOptions(res);
			}
		}
	);
	// 筛选参数变化
	const handleFilterChange = (type: string, value: any) => {
		if (type === "table_name") {
			setSearchParams({
				...searchParams,
				table_name: value,
				table_comment: value,
				column_name: value,
				column_comments: value,
			});
		} else {
			setSearchParams({ ...searchParams, [type]: value });
		}
	};

	// 选择数据库名称
	const handleSelectDbName = (
		connectionId: number | string,
		dbName: string,
		connectionType: string
	) => {
		if (!dbName) {
			setSchemaOptions([]);
		}
		setSearchParams({
			...searchParams,
			database: dbName ? dbName : undefined,
			schema_name: undefined,
		});
		if (dbName) {
			getSchemaList(connectionId, dbName, connectionType);
		}
	};

	// 刷新
	const refresh = () => {
		if (isChecked) {
			let params = {
				...searchParams,
				connect: connectionIdsInClassification,
				limit: `${(searchParams.pageNum || 1) - 1}|${searchParams?.pageSize || 10}`,
			}
			// 去掉多余的两个参数，使请求参数中不携带，避免后端报错
			params.pageNum = undefined;
			params.pageSize = undefined;
			getTableList({
				...params,
			});
		} else {
			message.error(t("db.conn.explore.msg"));
		}
	}

	useEffect(() => {
		if (isChecked) {
			let params = {
				...searchParams,
				connect: connectionIdsInClassification,
				limit: `${(searchParams.pageNum || 1) - 1}|${searchParams?.pageSize || 10}`,
			}
			// 去掉多余的两个参数，使请求参数中不携带，避免后端报错
			params.pageNum = undefined;
			params.pageSize = undefined;
			getTableList({
				...params,
			});
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [searchParams, isChecked]);

	useEffect(() => {
		// 左侧分级切换，重置状态
		setSearchParams({
			...defaultSearchParams
		});
		setIsChecked(false);
		checkDataProfiling(connectionIdsInClassification).then(() => {
			setIsChecked(true);
			
		}).catch(() => {
			setTableList([]);
			setTotal(0);
			message.error(t("db.conn.explore.msg"));
		})
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [connectionIdsInClassification])


	return {
		tableList: tableList || [],
		total: total || 0,
		loading,
		schemaOptions,
		searchParams,
		refresh,
		handleFilterChange,
		getSchemaList,
		setSearchParams,
		handleSelectDbName,
	};
};

export const useSelectedRow = () => {
	const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
	return {
		selectedRowKeys,
		setSelectedRowKeys,
	};
};
