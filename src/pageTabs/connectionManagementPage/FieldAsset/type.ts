export interface FieldAssetSearchParams {
    /**
     * 目录类型
     */
    catalog_type: string;
    /**
     * 列备注
     */
    column_comments?: string;
    /**
     * 列名称
     */
    column_name?: string;
    /**
     * 连接
     */
    connect?: string;
    /**
     * database
     */
    database?: string;
    /**
     * 数量
     */
    limit?: string;
    /**
     * 查询类型
     */
    query_type: string;
    /**
     * 表备注
     */
    table_comments?: string;
    /**
     * 表名称
     */
    table_name?: string;
    /**
     * 页码
     */
    pageNum?: number;
    /**
     * 每页数量
     */
    pageSize?: number;
    [property: string]: any;
}