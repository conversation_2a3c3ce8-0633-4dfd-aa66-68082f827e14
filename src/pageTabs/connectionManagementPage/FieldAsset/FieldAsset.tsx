import React from "react";
import { Table, Input, Select, Button } from "antd";
import { SearchOutlined, DownloadOutlined, RedoOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import styles from "./index.module.scss";
import ExportModal from "../ExportModal/ExportModal";
import { useAction, useDataSource, useSelectedRow } from "./index";
import { useTablePagination } from "src/hook";
import { getScrollX } from "src/util";

export const FieldAsset = (props: {
  connectionId: string;
  connectionType: string;
  databaseOptions: any;
  connectionIdsInClassification: string;
}) => {
  const { connectionId, connectionType, databaseOptions, connectionIdsInClassification } = props;
  const { t } = useTranslation();
  const { handleExport } = useAction();
  const {
    tableList,
    loading,
    schemaOptions,
    searchParams,
    total,
    handleFilterChange,
    handleSelectDbName,
    setSearchParams,
    refresh
  } = useDataSource(connectionIdsInClassification, connectionId);



  const { selectedRowKeys, setSelectedRowKeys } = useSelectedRow();
  const { pagination, handleChange } = useTablePagination(
    {
      current: searchParams.pageNum || 0,
      pageSize: searchParams.pageSize || 10,
      showSizeChanger: true,
      showQuickJumper: true,
    },
    searchParams,
    setSearchParams
  );

  // 表格列定义
  const columns = [
    {
      title: t("db.conn.fieldAsset.fieldName"),
      dataIndex: "table_name",
      key: "table_name",
      render: (val: string) => val || "-",
      with: 150,
    },
    {
      title: t("db.conn.fieldAsset.fieldRemark"),
      dataIndex: "table_comment",
      key: "table_comment",
      render: (val: string) => val || "-",
      with: 150,
    },
    {
      title: t("db.conn.fieldAsset.fieldType"),
      dataIndex: "data_type",
      key: "data_type",
      render: (val: string) => val || "-",
      with: 150,
    },
    {
      title: t("db.conn.fieldAsset.fieldLength"),
      dataIndex: "data_length",
      key: "data_length",
      render: (val: string) => val || "-",
      with: 150,
    },
    {
      title: t("db.conn.fieldAsset.fieldPrecision"),
      dataIndex: "data_precision",
      key: "data_precision",
      render: (val: string) => val || "-",
      with: 150,
    },
    {
      title: t("db.conn.fieldAsset.belongingTable"),
      dataIndex: "table_name",
      key: "table_name",
      render: (val: string) => val || "-",
      with: 150,
    },
    {
      title: t("db.conn.fieldAsset.tableRemark"),
      dataIndex: "table_comment",
      key: "table_comment",
      render: (val: string) => val || "-",
      with: 150,
    },
    {
      title: t("db.conn.fieldAsset.belongingSchema"),
      dataIndex: "schema_name",
      key: "schema_name",
      render: (val?: string) => val || "-",
      with: 150,
    },
    {
      title: t("db.conn.fieldAsset.belongingDatabase"),
      dataIndex: "database_name",
      key: "database_name",
      with: 150,
    },
  ];

  // 表格配置
  const tableProps = {
    dataSource: tableList,
    columns: columns,
    loading,
    rowKey: "id",
    pagination: {
      ...pagination,
      total:total,
      showTotal: () => `共 ${total} 条`,
    },
    rowSelection: {
      selectedRowKeys: selectedRowKeys,
      onChange: (newSelectedRowKeys: React.Key[]) => {
        setSelectedRowKeys(newSelectedRowKeys as string[]);
      },
    },
    scroll: { x: getScrollX(columns), y: "calc(100vh - 300px)" },
    onChange: handleChange,
  };

  return (
    <>
      {/* 表头筛选区域及导出 */}
      <div className={styles.assetManagementWrap}>
        <Input
          placeholder={t("db.conn.fieldAsset.inputPlaceholder")}
          onChange={(e) => handleFilterChange("table_name", e.target.value)}
          allowClear
          style={{ width: 350, marginLeft: 8 }}
          suffix={<SearchOutlined />}
        />
        <Select
          placeholder={t("db.conn.selectPlaceholder1")}
          onChange={(val: string) =>
            handleSelectDbName(connectionId, val, connectionType)
          }
          options={databaseOptions}
          style={{ width: 350, marginLeft: 8 }}
          allowClear
          value={searchParams.database}
        />
        <Select
          placeholder={t("db.conn.selectPlaceholder2")}
          onChange={(val) => handleFilterChange("schema_name", val)}
          options={schemaOptions}
          style={{ width: 350, marginLeft: 8 }}
          allowClear
          value={searchParams.schema_name}
        />
        {/* 刷新按钮 */}
        <Button
          type="primary"
          icon={<RedoOutlined />}
          // loading={loadings[2]}
          onClick={() => refresh()}
        />
        {/* 导出按钮靠右 */}
        <div style={{ marginLeft: "auto", display: "flex", gap: 8 }}>
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleExport}
            disabled={selectedRowKeys.length === 0}
          >
            {t("common.btn.export")}
          </Button>
        </div>
      </div>
      {/* 表格 */}
      <Table {...tableProps} />
      <ExportModal selectedRowKeys={selectedRowKeys} setSelectedRowKeys={setSelectedRowKeys} />

    </>
  );
};
