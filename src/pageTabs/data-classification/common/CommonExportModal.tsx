import React from 'react';
import { useTranslation } from 'react-i18next';
import { Form, Input } from 'antd';
import { UIModal } from 'src/components';
import { FormLayout } from '../constants';

interface IFormValues {
  filename: string;
}
export default ({
  submitBtnLoading = false,
  onExport,
  onCancel
}: {
  submitBtnLoading?: boolean;
  onExport: (params: IFormValues) => void;
  onCancel: () => void;
}) => {

  const { t } = useTranslation();
  const [industryForm] = Form.useForm<IFormValues>();

  const onSubmit = () => {
    industryForm.validateFields().then((values) => {
      onExport(values as IFormValues)
    })
  };

  return (
    <UIModal
      title={t('common.btn.export')}
      visible={true}
      onOk={onSubmit}
      onCancel={onCancel}
      confirmLoading={submitBtnLoading}
      width={600}
    >
      <Form form={industryForm} {...FormLayout}>
        <Form.Item
          label={t('classGrading.export.fileName.label')}
          name="filename"
          rules={[

            { required: true, pattern: /\.csv$/, message: t('classGrading.export.fileName.hint') }
          ]}
        >
          <Input placeholder={t('classGrading.export.fileName.plac')} />
        </Form.Item>
      </Form>
    </UIModal>
  );
}
