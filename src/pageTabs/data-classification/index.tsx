import React, { memo, useEffect, useState } from "react";
import classNames from 'classnames';
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary'
import { Layout } from 'antd'
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from "src/hook";
import { SimpleBreadcrumbs } from "src/components";
import { ClassificationTab } from './types';
import { DATA_CLASSIFICATION_TAB } from './constants';
import TemplateManagement from './templateManagement'
import TagManagement from './tagManagement'
import ClassificationTask from './classificationTask'
import ClassificationResult from "./classificationResult";
import { setDataClassificationState, setClassActiveTaKey } from "./dataClassificationSlice";
import styles from './index.module.scss'

export default memo(() => {

  const { t } = useTranslation();
  const dispatch = useDispatch();

  const { classActiveTaKey } = useSelector(state => state.dataClassification);
 
  useEffect(() => {
    return () => {
      dispatch(setClassActiveTaKey('templateManagement'));
    }
  }, []);

  const breadcrumbData = [
    { title: t('classGrading.title') },
  ];

  return (
    <Layout className={classNames("cq-container", [styles.dataClassificationWrapper])}>
      <div className={styles.dataClassificationHeader}>
        <SimpleBreadcrumbs items={breadcrumbData} />
        <div className={styles.tabsWrapper}>
          <div className={styles.tabs}>
            {
              Object.keys(DATA_CLASSIFICATION_TAB).map((tab) => (
                <div
                  key={tab}
                  className={classNames({
                    [styles.tabItem]: true,
                    [styles.tabItemActive]: classActiveTaKey === tab,
                  })}
                  onClick={() => {
                    dispatch(setClassActiveTaKey(tab as ClassificationTab));
                    dispatch(setDataClassificationState(null))
                  }}
                >
                  {DATA_CLASSIFICATION_TAB[tab as ClassificationTab]}
                </div>
              ))
            }
          </div>
        </div>
      </div>
      <div className={styles.dataClassificationContent}>
        <ErrorBoundary>
          {classActiveTaKey === 'templateManagement' && <TemplateManagement />}
          {classActiveTaKey === 'tagManagement' && <TagManagement />}
          {classActiveTaKey === 'classificationTask' && <ClassificationTask />}
          {classActiveTaKey === 'classificationResult' && <ClassificationResult />}
        </ErrorBoundary>
      </div>
    </Layout>
  )
})