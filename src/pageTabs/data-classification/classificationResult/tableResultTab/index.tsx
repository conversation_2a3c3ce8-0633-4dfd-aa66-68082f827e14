import React, { useEffect, useState } from "react";
import type { TableRowSelection } from "antd/es/table/interface"
import * as _ from 'lodash';
import { useTranslation } from 'react-i18next';
import { Button, Table, Space, Input, message } from 'antd';
import { useRequest, useDispatch } from 'src/hook'
import { ErrorBoundary } from 'src/components';
import { showModal } from "src/store/extraSlice/modalVisibleSlice";
import {
  ClassResTableAPI,
  ClassResultTableExportAPI
} from 'src/api';
import { ColumnDetailModal, ClassificationDetailModal } from '../modals';
import ExportModal from '../../common/CommonExportModal';
import { getTablePaginatinLimitFiled } from '../../utils';
import columns from './columns';
import styles from './index.module.scss';

interface TagItem {
  key: string;
  name: string;
  level: string;
  category: string;
}
export default () => {

  const defaultTableParams = {
    table_name: '',
    table_comment: '',
    classify_name: '',
    src_id: '',
    pageSize: 10,
    pageNum: 1
  }

  const { t } = useTranslation();
  const dispatch = useDispatch();

  const [tableParams, setTableParams] = React.useState(defaultTableParams);
  const [exportModalVisible, setExportModalVisible] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<React.Key[]>([]);

  //列表
  const { data: columnData, loading: listLoading, run: runGetTableListList, refresh: refreshTaskList } = useRequest((params: any) => {

    let newParams = {
      ...params,
      limit: getTablePaginatinLimitFiled(params?.pageNum, params?.pageSize)
    }

    delete newParams?.pageSize;
    delete newParams?.pageNum;
    return ClassResTableAPI(newParams);
  }, {
    manual: true,
    debounceInterval: 300,
    formatResult: (res: any) => {
      return {
        total: res?.count?.data?.[0]?.[0] || 0,
        list: res?.datas ?? []
      }
    }
  });

    //导出
    const { run: runExportColumn } = useRequest(ClassResultTableExportAPI, {
      manual: true,
      onSuccess: () => {
       message.success(t('common.export_success'))
       setExportModalVisible(false);
      }
    })

    
  useEffect(() => {
    runGetTableListList({...tableParams});
  }, [tableParams])

  const onViewColumnDetail = (record: any) => {
    dispatch(showModal('ClassResultColumnDetailModal', { record }))
  }

  const onViewClassDetail = (record: any) => {
    dispatch(showModal('ClassificationDetailModal', { record }))
  }

  //翻页多选
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: selectedRowKeys,
    getCheckboxProps: (record: any) => ({
      disabled: record?.sourceType === 'AUTO_USER' // 自动授权禁用
    }),
    onSelectAll(selected, newSelectedRows: any[]) {

      const curRowKeys = newSelectedRows.map(row => row?.permissionId);

      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      if (selected) {
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat(curRowKeys)
      } else {
        const curKeys = columnData?.list?.map((row: any) => row?.permissionId) || [];
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => !curKeys.includes(k))

      }
      setSelectedRowKeys([...new Set(cloneSelectedRowKeys)]);
    },
    onSelect(item, selected) {

      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      if (selected) {
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat([item.permissionId])
      } else {
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => k !== item.permissionId)
      }
      setSelectedRowKeys(cloneSelectedRowKeys);
    },
  };
  return (
    <div className={styles.columnResultWrapper}>
      <div className={styles.header}>
        <div className={styles.left}>
          <Space>
            <Input.Search
              placeholder={t('classGrading.tab.result.tableSearch.plac')}
              allowClear
              onSearch={(val: string) => setTableParams({ ...tableParams, pageNum: 1 })}
              className={styles.searchInput}
            />
          </Space>
        </div>
        <Button type='primary' onClick={() => setExportModalVisible(true)}>{t('common.btn.export')}</Button>
      </div>
      <div className={styles.content}>
        <ErrorBoundary>
          <Table
            loading={listLoading}
            className={styles.table}
            columns={columns({
              onViewColumnDetail,
              onViewClassDetail
            })}
            dataSource={columnData?.list || []}
            scroll={{
              y: `calc(100vh - 450px)`,
              x: 'max-content' // 添加横向滚动
            }}
            rowSelection={rowSelection}
            pagination={{
              showQuickJumper: true,
              showSizeChanger: true,
              total: columnData?.total || 0,
              showTotal: (total) => t('common.table.pagination.total', { total }),
              onChange: (pageNumber, pageSize = 10) => {
                setTableParams({ ...tableParams, pageNum: pageNumber, pageSize });
              },
            }}
          />
        </ErrorBoundary>
      </div>
      {/* 新增 编辑 */}
      {
        exportModalVisible &&
        <ExportModal
          onExport={() => { }}
          onCancel={() => setExportModalVisible(false)}
        />
      }
      {/* 列信息   后续连接管理有使用  */}
      <ColumnDetailModal />
      {/* 定级详情 */}
      <ClassificationDetailModal />

    </div>
  )
}

