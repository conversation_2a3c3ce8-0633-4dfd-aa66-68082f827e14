import React from "react";
import { <PERSON>, Button } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useTranslation } from "react-i18next";

export default ({
  onViewColumnDetail,
  onViewClassDetail
}: {
  onViewColumnDetail: (record: any) => void;
  onViewClassDetail: (record: any) => void;
}): ColumnsType<any> => {

  const { t } = useTranslation();
  
  return [
    {
      title: t('classGrading.tab.result.column.tableName1'),
      dataIndex: 'table_name',
      key: 'table_name',
      width: 100,
    },
    {
      title: t('classGrading.tab.result.column.tableDesc'),
      dataIndex: 'table_comment',
      key: 'table_comment',
      width: 100,
    },
    {
      title: t('classGrading.tab.result.column.class'),
      dataIndex: 'classify_name',
      key: 'classify_name',
      width: 100,
    },
    {
      title: t('classGrading.tab.result.column.manualClassName'),
      dataIndex: 'classify_name_human',
      key: 'classify_name_human',
      width: 100,
    },

    {
      title: t('classGrading.tab.result.column.classCount'),
      dataIndex: 'classify_id_count',
      key: 'classify_id_count',
      width: 100,
    },
    {
      title: t('classGrading.tab.result.column.totalColumnCount'),
      dataIndex: 'column_name_count',
      key: 'column_name_count',
      width: 100,
    },
    {
      title: t('classGrading.tab.result.column.classPercentage'),
      dataIndex: 'classify_percent',
      key: 'classify_percent',
      width: 100,
    },
    {
      title: t('classGrading.tab.result.column.level'),
      dataIndex: 'level_name',
      key: 'level_name',
      width: 100,
    },
    {
      title: t('classGrading.tab.result.column.schema'),
      dataIndex: 'schema_name',
      key: 'schema_name',
      width: 100,
    },
    {
      title: t('classGrading.tab.result.column.database'),
      dataIndex: 'database_name',
      key: 'database_name',
      width: 100,
    },
    {
      title: t('classGrading.tab.result.column.connection'),
      dataIndex: 'src_name',
      key: 'src_name',
      width: 100,
    },
    {
      title: t('classGrading.tab.result.column.connectionType'),
      dataIndex: 'src_Type',
      key: 'src_Type',
      width: 100,
    },
    {
      title: t('classGrading.tab.result.column.address'),
      dataIndex: 'src_host',
      key: 'src_host',
      width: 100,
    },
    {
      title: t('common.text.action'),
      dataIndex: 'action',
      key: 'action',
      width: 100,
      fixed: 'right',
      render: (_: any, record: any) => (
        <Space>
           <Button type='link' className="padding0" onClick={() =>onViewColumnDetail(record)}>{t('classGrading.tab.result.columnInfo')}</Button>
           <Button type='link' className="padding0" onClick={() =>onViewClassDetail(record)}>{t('classGrading.tab.result.column.view')}</Button>
        </Space>
      )
    },
  ]
}