import React from "react";
import i18n from "i18next";
import type { ColumnsType } from 'antd/es/table';

export default (): ColumnsType<any> => {

  return [
    {
      title: i18n.t('classGrading.tab.result.column.columnName'),
      dataIndex: 'column_name',
      key: 'column_name',
      fixed: 'left',
      width: 100
    },
    {
      title: i18n.t('classGrading.tab.result.column.columnDesc'),
      dataIndex: 'column_comment',
      key: 'column_comment',
      width: 100
    },
    {
      title: i18n.t('classGrading.tab.result.column.tag'),
      dataIndex: 'label_name',
      key: 'label_name',
      width: 100
    },
    {
      title: i18n.t('classGrading.tab.result.column.class'),
      dataIndex: 'classify_name',
      key: 'classify_name',
      width: 100
    },
    {
      title: i18n.t('classGrading.tab.result.column.model'),
      dataIndex: 'model_name',
      key: 'model_name',
      width: 100
    },
    {
      title: i18n.t('classGrading.tab.result.column.grade'),
      dataIndex: 'level_name',
      key: 'level_name',
      width: 150,
    },
    {
      title: i18n.t('classGrading.tab.result.column.table'),
      dataIndex: 'table_name',
      key: 'table_name',
      width: 100
    },
    {
      title: i18n.t('classGrading.tab.result.column.tableDesc'),
      dataIndex: 'table_comment',
      key: 'table_comment',
      width: 100
    },
    {
      title: i18n.t('classGrading.tab.result.column.schema'),
      dataIndex: 'schema_name',
      key: 'schema_name',
      width: 100
    },
    {
      title: i18n.t('classGrading.tab.result.column.database'),
      dataIndex: 'database_name',
      key: 'database_name',
      width: 120
    },
    {
      title: i18n.t('classGrading.tab.result.column.connection'),
      dataIndex: 'name',
      key: 'name',
      width: 100
    },
    {
      title: i18n.t('classGrading.tab.result.column.connectionType'),
      dataIndex: 'name',
      key: 'name',
      width: 100
    },
    {
      title: i18n.t('classGrading.tab.result.column.address'),
      dataIndex: 'src_host',
      key: 'src_host',
      width: 100
    },
    {
      title: i18n.t('classGrading.tab.result.column.classTask'),
      dataIndex: 'src_name',
      key: 'src_name',
      width: 150
    },
  ]
}