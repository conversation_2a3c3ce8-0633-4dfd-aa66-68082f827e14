import React, { useEffect, useState } from "react";
import * as _ from 'lodash';
import { useTranslation } from 'react-i18next';
import type { TableRowSelection } from "antd/es/table/interface"
import { Button, Table, Space, Input, Select, message } from 'antd';
import { ErrorBoundary } from 'src/components';
import { useRequest, useDispatch } from 'src/hook'
import {
  ClassResColumnAPI,
  ClassTaskIndustryTypeAPI,
  ClassResultColExportAPI,
  ClassTagTaskAPI
} from 'src/api';
import { ClassificationRes } from '../../types';
import { CLASSIFICATION_RES } from '../../constants';
import ExportModal from '../../common/CommonExportModal'
import { getTablePaginatinLimitFiled, generateKeyValueObjects } from '../../utils';
import columns from './columns';
import styles from './index.module.scss';

export default () => {

  const defaultTableParams = {
    pageNum: 1,
    pageSize: 10,
    table_name: '',
    table_comment: '',
    column_name: '',
    column_comment: '',
    classify_name: '',
    level_name: '',
    // type: 'ALL'
  }
  const mockData: any[] = []
  const { t } = useTranslation();

  const classResultOptions = Object.keys(CLASSIFICATION_RES).map(type => ({ label: CLASSIFICATION_RES[type as ClassificationRes], value: type }));

  const [tableParams, setTableParams] = React.useState(defaultTableParams);
  const [exportModalVisible, setExportModalVisible] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  //列表
  const { data: columnData, loading: listLoading, run: runGetColumnListList, refresh: refreshTaskList } = useRequest((params: any) => {

    let newParams = {
      ...params,
      limit: getTablePaginatinLimitFiled(params?.pageNum, params?.pageSize)
    }

    delete newParams?.pageSize;
    delete newParams?.pageNum;
    return ClassResColumnAPI(newParams);
  }, {
    manual: true,
    debounceInterval: 300,
    formatResult: (res: any) => {
      return {
        total: res?.count?.data?.[0]?.[0] || 0,
        list: res?.datas ?? []
      }
    }
  });
  //分类分级任务
  const { data: classTaskList } = useRequest(ClassTagTaskAPI, {
    debounceInterval: 300,
    formatResult: (res: any) => {
      return res?.datas?.map((item: any) => ({
        label: item?.task_name,
        value: item?.id
      }))
    }
  });

  //导出
  const { run: runExportColumn } = useRequest(ClassResultColExportAPI, {
    manual: true,
    onSuccess: () => {
     message.success(t('common.export_success'))
     setExportModalVisible(false);
    }
  })

  useEffect(() => {
    runGetColumnListList({...tableParams})

  },[tableParams])
  //翻页多选
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: selectedRowKeys,
    getCheckboxProps: (record: any) => ({
      disabled: record?.sourceType === 'AUTO_USER' // 自动授权禁用
    }),
    onSelectAll(selected, newSelectedRows: any[]) {

      const curRowKeys = newSelectedRows.map(row => row?.permissionId);

      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      if (selected) {
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat(curRowKeys)
      } else {
        const curKeys = mockData?.map((row: any) => row?.permissionId) || [];
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => !curKeys.includes(k))

      }
      setSelectedRowKeys([...new Set(cloneSelectedRowKeys)]);
    },
    onSelect(item, selected) {

      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      if (selected) {
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat([item.permissionId])
      } else {
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => k !== item.permissionId)
      }
      setSelectedRowKeys(cloneSelectedRowKeys);
    },
  };

  return (
    <div className={styles.columnResultWrapper}>
      <div className={styles.header}>
        <div className={styles.left}>
          <Space>
            <Input.Search
              placeholder={t('classGrading.tab.result.column.searchPlac')}
              allowClear
              onSearch={(val: string) => setTableParams({ ...tableParams, pageNum: 1 })}
              className={styles.searchInput}
            />
            <Select 
              allowClear 
              options={classTaskList} 
              placeholder={t('classGrading.tab.result.column.searchTask.plac')} 
              onChange={(val: string) => setTableParams({ ...tableParams,level_name: val, pageNum: 1 })}
            />
            <Select  options={classResultOptions} />
          </Space>
        </div>
        <Button type='primary' onClick={() => setExportModalVisible(true)}>{t('common.btn.export')}</Button>
      </div>
      <div className={styles.content}>
        <ErrorBoundary>
          <Table
            loading={listLoading}
            className={styles.table}
            columns={columns()}
            dataSource={columnData?.list || []}
            scroll={{
              y: `calc(100vh - 450px)`,
              x: 'auto' // 添加横向滚动
            }}
            rowSelection={rowSelection}
            pagination={{
              showQuickJumper: true,
              showSizeChanger: true,
              pageSize: tableParams?.pageSize || 10,
              current: tableParams?.pageNum || 1,
              total: columnData?.total || 0,
              showTotal: (total) => t('common.table.pagination.total', { total }),
              onChange: (pageNumber, pageSize = 10) => {
                setTableParams({ ...tableParams, pageNum: pageNumber, pageSize });
              },
            }}
          />
        </ErrorBoundary>
      </div>
      {/* 导出 */}
      {
        exportModalVisible &&
        <ExportModal
          onExport={(params: {filename: string}) => {runExportColumn(params) }}
          onCancel={() => setExportModalVisible(false)}
        />
      }
    </div>
  )
}

