import React, { useState } from "react";
import { useTranslation } from 'react-i18next';
import { Tabs } from 'antd';
import { useDispatch } from 'src/hook'
import { ErrorBoundary } from 'src/components';
import ColumnResultTab from './columnResultTab'
import TableResultTab from './tableResultTab'
import styles from './index.module.scss';

export default () => {

  const { t } = useTranslation();
 
  const [resultActiveTabKey, setResultActiveTabKey] = useState('columnResult');


  return (
    <div className={styles.classificationResult}>
      <div>
        <Tabs
          onChange={(key) => setResultActiveTabKey(key)}
          activeKey={resultActiveTabKey}
        >
          <Tabs.TabPane key='columnResult' tab={t('classGrading.tab.result.tab.column')} />
          <Tabs.TabPane key='tableResult' tab={t('classGrading.tab.result.tab.table')}/>
        </Tabs>
        <ErrorBoundary>
          {resultActiveTabKey === 'columnResult' && <ColumnResultTab />}
          {resultActiveTabKey === 'tableResult' && <TableResultTab />}
        </ErrorBoundary>
      </div>
    </div>
  )
}