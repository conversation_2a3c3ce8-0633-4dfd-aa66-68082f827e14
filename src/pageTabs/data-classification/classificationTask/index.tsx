import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from 'react-i18next';
import { Button, Table, message } from 'antd';
import { useRequest, useDispatch } from 'src/hook'
import { ErrorBoundary } from 'src/components';
import {
  ClassTagTaskAPI,
  ClassTagDeleteTaskAPI,
  ClassTaskAddTaskAPI,
  ClassTaskEditTaskAPI,
  ClassTaskExecuteTaskAPI,
  ClassTaskIndustryTypeAPI,
  IClassTaskExecuteTaskParams
} from 'src/api';
import { AddOrEditTaskModal } from './modals';
import { setDataClassificationState, setClassActiveTaKey } from '../dataClassificationSlice'
import columns from './columns'
import { createMapFromFieldArrays } from '../utils';
import styles from './index.module.scss';

export interface FieldMap {
  [key: string | number]: any;
}

export default () => {

  const { t } = useTranslation();
  const dispatch = useDispatch();
  //关联数据源映射
  const [datasourceMap, setDatasourceMap] = useState<FieldMap>({} as FieldMap)
  const [industryTypeMap, setIndustryTypesMap] = useState<FieldMap>({} as FieldMap)
  const [classTemplateMap, setClassTemplateMap] = useState<FieldMap>({} as FieldMap)
 
  const [addOrEditTaskModalVisible, setAddOrEditTaskModalVisible] = useState(false);
  const [editTaskInfo, setEditTaskInfo] = useState<any>(null);

  //列表
  const { data: taskData, loading: listLoading, run: runGetTaskList, refresh: refreshTaskList } = useRequest(ClassTagTaskAPI, {
    manual: true,
    debounceInterval: 300,
    formatResult: (res: any) => {
      return {
        total: res?.count?.data?.[0]?.[0] || 0,
        list: res?.datas ?? []
      }
    }
  });
    //查询关联数据源 做table映射
  const getDatasource = async () => {
    const res: any = await ClassTaskIndustryTypeAPI({ key: 'dd:data_dams_dbms' });
    const options = createMapFromFieldArrays(res?.datas?.data || [], res?.datas?.index);
    setDatasourceMap(options);
  }
  //行业类型
  const getIndustryTypes = async () => {
    const res: any = await ClassTaskIndustryTypeAPI({ key: 'dd:dams_label_type1' });
    const options = createMapFromFieldArrays(res?.datas?.data || [], res?.datas?.index);
    setIndustryTypesMap(options);
  }
  //分类分级模板
  const getClassTemplate = async () => {
    const res: any = await ClassTaskIndustryTypeAPI({ key: 'dd:data_dbms_classifys' });
    const options = createMapFromFieldArrays(res?.datas?.data || [], res?.datas?.index);
    setClassTemplateMap(options);
  }

  //删除任务
  const { run: runDelete } = useRequest(ClassTagDeleteTaskAPI, {
    manual: true,
    onSuccess: (res) => {
      message.success(t('common.message.delete_success'));
      refreshTaskList();
    }
  })
  //手动 执行任务
  const { run: runExecuteTask } = useRequest(ClassTaskExecuteTaskAPI, {
    manual: true,
    onSuccess: (res) => {
      message.success(t('common.message.executeSuccess'));
      refreshTaskList();
    }
  })
  //新增 编辑任务
  const { loading: addOrEditBtnLoading, run: runAddOrEditTask } = useRequest((params) => {
    if (params?.id) {
      return ClassTaskEditTaskAPI(params);
    }
    return ClassTaskAddTaskAPI(params);
  }, {
    manual: true,
    onSuccess: () => {
      if (editTaskInfo) {
        message.success(t('common.message.editSuccess'));
      } else {
        message.success(t('common.message.addSuccess'));
      }
      setAddOrEditTaskModalVisible(false);
      setEditTaskInfo(null);
      refreshTaskList();
    }
  })

  useEffect(() => {
    runGetTaskList();
    getDatasource();
    getIndustryTypes();
    getClassTemplate();
  }, [])

  //编辑
  const onEdit = (record: any) => {
    setAddOrEditTaskModalVisible(true);
    setEditTaskInfo(record)
  }

  const onHandleLinkToResult = (record: any) => {
    dispatch(setClassActiveTaKey('classificationResult'))
    dispatch(setDataClassificationState({id: record?.id}))
  }

  return (
    <div className={styles.classificationTask}>
      <div className={styles.header}>
        <Button type='primary' onClick={() => { setAddOrEditTaskModalVisible(true) }}>{t('common.btn.add')}</Button>
      </div>
      <div className={styles.content}>
        <ErrorBoundary>
          <Table
            key='id'
            loading={listLoading}
            className={styles.table}
            columns={columns({
              datasourceMap,
              industryTypeMap,
              classTemplateMap,
              onEdit,
              onExecuteTask: (params: IClassTaskExecuteTaskParams) => runExecuteTask(params),
              onDeleteTask: (id: number) => runDelete({ id }),
              onHandleLinkToResult
            })}
            dataSource={taskData?.list || []}
            scroll={{
              y: `calc(100vh - 500px)`,
              x: 'max-content' // 添加横向滚动
            }}
            pagination={{
              showQuickJumper: true,
              showSizeChanger: true,
              total: taskData?.total || 0,
              showTotal: (total) => t('common.table.pagination.total', { total }),
            }}
          />
        </ErrorBoundary>
      </div>
      {/* 新增 编辑 */}
      {
        addOrEditTaskModalVisible &&
        <AddOrEditTaskModal
          editTaskInfo={editTaskInfo}
          submitBtnLoading={addOrEditBtnLoading}
          onSubmit={((params: any) => runAddOrEditTask(params))}
          onCancel={() => { setAddOrEditTaskModalVisible(false); setEditTaskInfo(null); }}
        />
      }
    </div>
  )
}
