import React, { useEffect, useState } from "react";
import moment from 'moment'
import { useTranslation } from "react-i18next";
import { Form, Input, Select, Radio, TimePicker, message } from "antd";
import { UIModal } from "src/components";
import { useRequest } from 'src/hook';
import {
  getClassNewTemplateAPI,
  ClassTaskIndustryTypeAPI,
  ClassTaskDatasourceAPI,
  ClassBuiltTempItem
} from 'src/api';
import {
  CLASSIFICATION_SCHEDULE_CYCLE,
  FormLayout,
  TASK_MODE_MAPPING,
  EXECUTE_TYPE_MAPPING,
  TASK_TYPE_MAPPING
} from '../../constants';
import { TaskSheduleCycle } from '../../types';
import { generateKeyValueObjects, formatKeyMapToOption } from '../../utils';

export default ({
  editTaskInfo,
  submitBtnLoading = false,
  onCancel,
  onSubmit
}: {
  editTaskInfo: any;
  submitBtnLoading: boolean
  onCancel: () => void;
  onSubmit: (params: any) => void;
}) => {

  const { t } = useTranslation();
  const [taskForm] = Form.useForm();

  //关联数据源
  const [datasources, setDatasources] = React.useState<any[]>([]);
  //行业类型
  const [industryTypes, setIndustryTypes] = React.useState<any[]>([]);
  //关联
  const [classTemplates, setClassTemplates] = useState<any[]>([]);

  //执行周期
  const cycleOptions = formatKeyMapToOption(CLASSIFICATION_SCHEDULE_CYCLE);
  //执行策略
  const TaskModeOptions = formatKeyMapToOption(TASK_MODE_MAPPING);
  //任务类型
  const ExecuteTypeOptions = formatKeyMapToOption(EXECUTE_TYPE_MAPPING);
  //识别方式
  const TaskTypeOptions = formatKeyMapToOption(TASK_TYPE_MAPPING);

  //关联数据源
  const getDatasource = async () => {
    const res: any = await ClassTaskIndustryTypeAPI({ key: 'dd:data_dams_dbms' });
    const options = generateKeyValueObjects(res?.datas?.data || [], res?.datas?.index);
    setDatasources(options);
  }
  //行业类型
  const getIndustryTypes = async () => {
    const res: any = await ClassTaskIndustryTypeAPI({ key: 'dd:dams_label_type1' });
    const options = generateKeyValueObjects(res?.datas?.data || [], res?.datas?.index);
    setIndustryTypes(options);
  }
  //分类分级模板
  const getClassTemplate = async () => {
    const res: any = await ClassTaskIndustryTypeAPI({ key: 'dd:data_dbms_classifys' });
    const options = generateKeyValueObjects(res?.datas?.data || [], res?.datas?.index);
    setClassTemplates(options);
  }

  useEffect(() => {
    getDatasource();
    getIndustryTypes();
    getClassTemplate();
  }, [])

  useEffect(() => {
    taskForm.setFieldsValue({ 
      ...editTaskInfo,
      ...(editTaskInfo?.schedule_time ? {schedule_time :moment(editTaskInfo?.schedule_time, 'HH:mm:ss')} : {}  )
     });
  }, [editTaskInfo])

  const onFormValidate = () => {
    taskForm.validateFields().then((values) => {
      onSubmit({
        ...values,
        tp_id_c: 0,
        task_reset: 0,
        recon_id: 0,
        label_id: 0,
        src_ids: [],
        task_schedule: 0, //先传0 他们j数据库字段没改
      });
    }).catch(() => { });
  }

  return (
    <UIModal
      visible={true}
      title={editTaskInfo ? t('classGrading.tab.task.editTask') : t('classGrading.tab.task.addTask')}
      width={600}
      onCancel={onCancel}
      onOk={onFormValidate}
      confirmLoading={submitBtnLoading}
    >
      <Form form={taskForm} {...FormLayout}>
        <Form.Item
          required
          label={t('classGrading.tab.task.column.taskName')}
          name='task_name'
          rules={[{ required: true, message: t('classGrading.tab.task.taskName.plac') }]}
        >
          <Input placeholder={t('classGrading.tab.task.taskName.plac')} />
        </Form.Item>
        <Form.Item
          required
          label={t('classGrading.tab.task.column.dataSource')}
          name='src_id'
          rules={[{ required: true, message: t('classGrading.tab.task.dataSource.plac') }]}
        >
          <Select placeholder={t('classGrading.tab.task.dataSource.plac')} disabled={editTaskInfo} options={datasources} />
        </Form.Item>
        <Form.Item
          required
          label={t('classGrading.tab.task.column.industryType')}
          name='label_ids'
          rules={[{ required: true, message: t('classGrading.tab.task.industryType.plac') }]}

        >
          <Select placeholder={t('classGrading.tab.task.industryType.plac')} disabled={editTaskInfo} options={industryTypes} />
        </Form.Item>
        <Form.Item
          required
          label={t('classGrading.tab.task.column.template')}
          name='classifys_id'
        >
          <Select placeholder={t('classGrading.tab.task.template.plac')} disabled={editTaskInfo} options={classTemplates} />
        </Form.Item>
        <Form.Item
          required
          label={t('classGrading.tab.task.column.executeType')}
          name='task_type'
          initialValue={0}
        >
          <Radio.Group options={ExecuteTypeOptions} />
        </Form.Item>
        <Form.Item
          noStyle
          dependencies={['task_type']}
        >
          {
            ({ getFieldValue }) => {
              const isEngineIdentify = getFieldValue('task_type') === 0;

              return isEngineIdentify && (
                <Form.Item
                  required
                  label={t('classGrading.tab.task.identify_type.label')}
                  name='identify_type'
                >
                  <Select options={TaskTypeOptions} placeholder={t('classGrading.tab.task.identify_type.plac')} />
                </Form.Item>
              )
            }
          }
        </Form.Item>
        <Form.Item
          required
          label={t('classGrading.tab.task.column.executeStrategy')}
          name='task_mode'
          initialValue={2}
        >
          <Radio.Group options={TaskModeOptions} />
        </Form.Item>
        <Form.Item
          required
          label={t('classGrading.tab.task.column.scheduleCycle')}
          name='task_schedule'
          rules={[{ required: true, message: t('classGrading.tab.task.scheduleCycle.plac') }]}
        >
          <Select options={cycleOptions} placeholder={t('classGrading.tab.task.scheduleCycle.plac')} />
        </Form.Item>
        <Form.Item noStyle dependencies={['task_schedule']}>
          {
            ({ getFieldValue }) => {
              const task_schedule_value = getFieldValue('task_schedule');
              const isNotManual: boolean = task_schedule_value && task_schedule_value !== 10000;

              return isNotManual &&
                <Form.Item
                  label={t('classGrading.tab.task.executeTime')}
                  name='schedule_time'
                  rules={[{ required: true, message: t('classGrading.tab.task.executeTime.plac') }]}
                >
                  <TimePicker placeholder={t('classGrading.tab.task.executeTime.plac')} />
                </Form.Item>
            }
          }
        </Form.Item>
      </Form>
    </UIModal>
  )
}