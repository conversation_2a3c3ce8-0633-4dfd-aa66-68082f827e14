.classificationTask {
  .header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  .content {
    .table {
      :global {
        .ant-table-cell {
          white-space: nowrap;
        }
      }

      .status {
        padding: 2px 10px;
        color: #fff;
        border-radius: 5px;
        display: inline-block;
        white-space: nowrap;
        background: #708090;
      }

      .status-0 {
        background: #708090;
      }
      .status-9 {
        background: #87D068;
      }
      .status-1 {
        background: #FFE4C4;
      }

      .status-pending {
        background: #ffe4c4;
      }

      .status-failed {
        color: #E55174;
        background: #FFEBEB;
      }

      .status-success {
        background: #E9F7F9;
        color: #41AA9F;
      }
    }
  }
}