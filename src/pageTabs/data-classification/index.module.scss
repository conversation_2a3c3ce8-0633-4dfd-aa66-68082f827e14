@import 'src/styles/variables.scss';

.dataClassificationWrapper {
  padding: 0 18px 24px 24px;
  height: calc(100vh - 30px - 48px);
  .dataClassificationHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .tabsWrapper {
      flex: 1;

      .tabs {
        width: 60%;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: space-around;
        cursor: pointer;

        .tabItem {
          padding: 12px 6px;
          font-size: 16px;
          color: #0f244c;
        }

        .tabItemActive {
          border-bottom: 3px solid var(--primary-color);
          
        }
      }
    }
  }

  .dataClassificationContent {
    margin-top: 30px;
    min-height: 300px;
  }
}