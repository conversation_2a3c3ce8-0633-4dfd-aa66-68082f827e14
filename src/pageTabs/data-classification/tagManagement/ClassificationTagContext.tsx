import React, { useContext } from 'react'

interface IClassificationTagContext {
  selectedTreeNodeInfo?: any;
  setSelectedTreeNodeInfo: (nodeInfo: any) => void;
  editItemInfo: any;
}

export const ClassificationTagContext = React.createContext<IClassificationTagContext>({
  selectedTreeNodeInfo: null,//选中树节点
  setSelectedTreeNodeInfo: () => { }, //设置选中树节点
  editItemInfo: null,//编辑项信息
})

export const useClassificationTagContext = () => {
  const { selectedTreeNodeInfo, setSelectedTreeNodeInfo, editItemInfo } = useContext(ClassificationTagContext);
  return { selectedTreeNodeInfo, setSelectedTreeNodeInfo,  editItemInfo };
}

