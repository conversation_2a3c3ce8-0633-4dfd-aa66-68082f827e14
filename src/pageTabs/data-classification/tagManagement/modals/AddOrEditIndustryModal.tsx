import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, Input, Select, message } from 'antd';
import type { FormInstance } from 'antd/es/form';
import { useRequest } from 'src/hook';
import {
  DataSourceType,
  getClassDatasourceAPI
} from 'src/api';
import { UIModal } from 'src/components';
import { FormLayout, INDDUSTRY_TAG_TYPE, CreateIndustryTagType } from '../../constants';
import { generateKeyValueObjects } from '../../utils';

interface TemplateFormValues {
  templateName: string;
  tagType?: string;
  relatedTemplate?: string;
}

export default ({
  editTtemInfo,
  onCancel,
  onSubmit
}: {
  editTtemInfo: any;
  onCancel: () => void;
  onSubmit: (params: any) => void;
}) => {

  const { t } = useTranslation();
  const [industryForm] = Form.useForm<TemplateFormValues>();

  const createDataTagOptions = Object.keys(INDDUSTRY_TAG_TYPE).map((type) => ({
    label: INDDUSTRY_TAG_TYPE[Number(type) as CreateIndustryTagType],
    value: Number(type)
  }))
  const { data: datasource, run: runGetClassDatasourceAPI } = useRequest(getClassDatasourceAPI, {
    manual: true,
    formatResult(res: any) {
      if (!res?.datas?.data?.length || !res?.datas?.index) return []
      return generateKeyValueObjects(res.datas?.data, res.datas.index)
    },
  })

  useEffect(() => {
    if (!industryForm) return;
    industryForm.setFieldsValue({
      ...editTtemInfo
    })
  }, [industryForm]);

  const onValidateForm = () => {
    industryForm.validateFields().then((values) => {
      console.log(values, 'values==')
      onSubmit({
        ...values,
        select_id:values?.select_id ?? 1, //默认传1
        ...(editTtemInfo ? { 
          id: editTtemInfo?.id 
        } : {})
      })

    })
  };

  return (
    <UIModal
      title={editTtemInfo ? t('common.btn.edit') : t('common.btn.add')}
      visible={true}
      onOk={onValidateForm}
      onCancel={onCancel}
      confirmLoading={false}
      width={600}
    >
      <Form form={industryForm} {...FormLayout}>
        <Form.Item
          label={t('classGrading.tab.tag.name.label')}
          name="name"
          rules={[{ required: true, message: t('classGrading.tab.tag.name.plac') }]}
        >
          <Input placeholder={t('classGrading.tab.tag.name.plac')} />
        </Form.Item>

        <Form.Item
          label={t('classGrading.tab.tag.desc.label')}
          name="details"
        >
          <Input.TextArea rows={3} placeholder={t('classGrading.tab.tag.desc.plac')} allowClear />
        </Form.Item>

        {
          !editTtemInfo &&

          (
            <>
              <Form.Item
                label={t('classGrading.tab.tag.createTag.label')}
                name="select_id"
                rules={[
                  {
                    validator(rule, value, callback) {
                      if (value && (Number(value) as CreateIndustryTagType === 2)) {
                        runGetClassDatasourceAPI({ key: 'dd:data_dams_dbms' })
                      }
                      callback();
                    },
                  }
                ]}
              >
                <Select options={createDataTagOptions} placeholder={t('classGrading.tab.tag.createTag.plac')} allowClear />
              </Form.Item>
              <Form.Item
                noStyle
                dependencies={['select_id']}
              >
                {
                  ({ getFieldValue }) => {
                    const selectId = getFieldValue('select_id');
                    if (selectId && Number(selectId) === 2 ) {

                      return (
                        <Form.Item
                          required
                          label={t('classGrading.tab.tag.dataSource.label')}
                          name="src_id"
                          rules={[{ required: true, message: t('classGrading.tab.tag.dataSource.plac') }]}
                        >
                          <Select options={datasource} placeholder={t('classGrading.tab.tag.dataSource.plac')} allowClear />
                        </Form.Item>
                      )
                    }
                  }
                }
              </Form.Item>
            </>
          )
        }
      </Form>
    </UIModal>
  );
}
