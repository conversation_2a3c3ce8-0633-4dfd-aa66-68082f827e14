import React, { useEffect } from "react";
import { Form, Input, Radio, Select, Row, Col, InputNumber } from 'antd';
import { useTranslation } from 'react-i18next';
import { useRequest } from 'src/hook';
import { ClassStorageAPI } from 'src/api';
import { UIModal } from 'src/components';
import { 
  FetchIndustryListParams, 
  ClassTaskFormFields, 
  PERSONAL_BOOL_MAPPING,
  PROFESSION_BOOL_MAPPING,
  IMPORTANT_DATA_MAPPING,
  CORD_DATA_MAPPING
} from '../../constants';
import { formatKeyMapToOption } from '../../utils';
import styles from './index.module.scss';

const TagModal = ({
  editTagInfo,
  onCancel,
  onSubmit
}: {
  editTagInfo: any
  onCancel: () => void;
  onSubmit: (values: any) => void;
}) => {

  const [form] = Form.useForm();
  const { t } = useTranslation();
  //包含识别权重的字段
  const includeWeightFields = ClassTaskFormFields.map(field => field.valueName);
 //个人敏感信息
 const presonalBoolOptions =formatKeyMapToOption(PERSONAL_BOOL_MAPPING);
 //行业敏感信息
 const professionBoolOptions =formatKeyMapToOption(PROFESSION_BOOL_MAPPING);
 //行业敏感信息
 const importantDataOptions =formatKeyMapToOption(IMPORTANT_DATA_MAPPING);
 //行业敏感信息
 const coreDataOptions =formatKeyMapToOption(CORD_DATA_MAPPING);

  // 行业类型
  const { data: industryTypeOptions, run: runClassStorageAPI } = useRequest(ClassStorageAPI,
    {
      manual: true,
      formatResult(res: any) {
        const datas = res?.datas || [];
        const formattedTreeData = datas.map((item: any) => ({
          label: item?.name,
          value: item?.type,
        }))

        return formattedTreeData;
      },
    });

  useEffect(() => {
    form.setFieldsValue({ ...editTagInfo })
  }, [editTagInfo])

  useEffect(() => {
    runClassStorageAPI({ ...FetchIndustryListParams });
  }, [])

  const onValidateForm = () => {
    form.validateFields().then(values => {
      // 将 undefined 值转换为空字符串
      const processedValues = Object.keys(values).reduce((acc, key) => {
        //weight 相关字段默认值为0
        acc[key] = values[key] === undefined ? includeWeightFields.includes(key) ? 0 : '' : values[key];
        return acc;
      }, {} as Record<string, any>);

      onSubmit({
        ...processedValues,
        action: editTagInfo ? 'edit_tag' : 'add_tag',
        label_status: 0,
        //type和index一致
        ...(editTagInfo ? { index: editTagInfo?.id } : {})
      })
    }).catch(info => {
      console.log('Validate Failed:', info);
    });
  }

  const onWeightValidate = (_: any, value: any) => {
    if (value === undefined || (value >= 0 && value <= 0.9)) {
      return Promise.resolve();
    }
    return Promise.reject(new Error(t('classGrading.tab.tag.threshold.hint')));
  }

  const onThresholdValidate = (_: any, value: any) => {
    if (value === undefined) {
      return Promise.reject(new Error(t('classGrading.tab.tag.threshold.plac')));
    }
    if (value >= 0 && value <= 0.9) {
      return Promise.resolve();
    }
    return Promise.reject(new Error(t('classGrading.tab.tag.threshold.hint2')));
  }

  return (
    <UIModal
      visible={true}
      title={editTagInfo ? t('classGrading.tab.tag.edit.title') : t('classGrading.tab.tag.addTag')}
      onCancel={onCancel}
      onOk={onValidateForm}
      bodyStyle={{ maxHeight: `calc(100vh - 290px)` }}
    >
      <Form form={form} colon={false} name="advanced-search-form" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
        <Row gutter={[20, 10]}>
          <Col span={12}>
            <Form.Item
              name="label_name"
              label={t('classGrading.tab.tag.label_name.label')}
              rules={[{ required: true, message: t('classGrading.tab.tag.label_name.plac') }]}
            >
              <Input placeholder={t('classGrading.tab.tag.label_name.plac')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="threshold"
              label={t('classGrading.tab.tag.column.totalMatchWeight')}
              rules={[{ validator: onThresholdValidate }]}
            >
              <InputNumber placeholder="0~0.9" className={styles.inputNumber}/>
            </Form.Item>
          </Col>
          {
            ClassTaskFormFields.map((field: any) => (
              <>
                <Col span={12} key={field.name}>
                  <Form.Item name={field.name} label={field.label} initialValue={field.defaultValue ?? ''}>
                    {
                      field.type === 'Input' ? (
                        <Input placeholder={field?.placeholder || t('classGrading.tab.tag.field.plac', { val: field?.label })} />
                      ) : (
                        <Select placeholder={field.placeholder} options={field?.options || []} />
                      )
                    }
                  </Form.Item>
                </Col>
                <Col span={12} key={field.name}>
                  <Form.Item
                    name={field.valueName}
                    label={t('classGrading.tab.tag.threshold.label')}
                    rules={[
                     {
                        validator: onWeightValidate
                      },
                    ]}>
                    <InputNumber placeholder="0.1~0.9" className={styles.inputNumber} />
                  </Form.Item>
                </Col>
              </>
            ))
          }
          <Col span={12}>
            <Form.Item
              name="personal_bool"
              label={t('classGrading.tab.tag.column.sensitive')}
              initialValue={0}
            >
              <Radio.Group optionType="button" buttonStyle="solid" options={presonalBoolOptions} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="profession_bool"
              label={t('classGrading.tab.tag.column.industry')}
              initialValue={0}
            >
              <Radio.Group optionType="button" buttonStyle="solid" options={professionBoolOptions}/>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="important_data"
              label={t('classGrading.tab.tag.column.importantData')}
              initialValue={0}
            >
              <Radio.Group optionType="button" buttonStyle="solid" options={importantDataOptions} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="core_data"
              label={t('classGrading.tab.tag.column.coreData')}
              initialValue={0}
            >
              <Radio.Group optionType="button" buttonStyle="solid" options={coreDataOptions} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="type"
              label={t('classGrading.tab.tag.column.industryType')}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 20 }}
              rules={[{ required: true, message: t('classGrading.tab.tag.type.plac') }]}
            >
              <Select options={industryTypeOptions} placeholder={t('classGrading.tab.tag.type.plac')} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </UIModal>
  )
}
export default TagModal;