@import 'src/styles/variables';

.tagManagement {
  display: flex;
  flex-direction: row;
  height: calc(100vh - 186px);
  background-color: #fff;

  .resizableBox {
    margin-right: 10px;
    height: 100% !important;
  }

  .resizeHandle {
    position: absolute;
    right: -13px;
    top: calc(50% - 24px);
    font-size: 16px;
    cursor: col-resize;
    color: rgba(0, 0, 0, 0.85);
  }
}

.leftWrap,
.rightWrap {
  background-color: #f7f9fc;
  border-radius: 4px;
  height: 100%;
}

.leftWrap {
  width: 100%;
  padding: 16px 14px;


  .treeHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;

    .headerImport {
      color: #3262FF;
    }
  }

  .tree {
    :global {
      .ant-tree-treenode {
        width: 100%;
        padding: 0;
        border-bottom: 4px solid #F7F9FC;
        background-color: #fff;
        height: 36px;
        align-items: center;
        border-radius: 4px;

        &:last-child {
          height: 32px;
          border-bottom: 0;
        }
      }

      .ant-tree-node-content-wrapper {
        flex: 1;
      }

      .ant-tree-node-content-wrapper:hover,
      .ant-tree-treenode:hover {
        background-color: #d6e5ff;
      }

      .ant-tree-node-content-wrapper {
        transition: none;
      }

      .ant-tree-treenode-selected {
        background-color: #d6e5ff;
      }

      .ant-tree-treenode-selected .ant-tree-switcher,
      .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected {
        color: initial;
        background-color: #d6e5ff;
      }
    }

    .treeTitleItem {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .titleTxtWrap {
        display: flex;
        flex: 1;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;

        .titleTxt {
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .options {
        cursor: pointer;
        color: var(--primary-color);

        :global {
          .ant-dropdown-menu-item {
            color: var(--primary-color);
          }
        }
      }
    }
  }

  .treeTitleWithIcon {
    white-space: nowrap;
    display: flex;
    align-items: center;

    :global .anticon {
      margin-right: 8px;
    }

    .treeTitleNumber {
      color: $text-support;
    }
  }

  .spinContainer {
    margin-top: 10px;
    border-radius: 4px;
    height: calc(100% - 40px);
    overflow-y: auto;
  }
  .treePlaceholder {
    text-align: center;
    margin-top: 24px;
    color: rgba(0, 0, 0, 0.75);
  }

}

.rightWrap {
  flex: 1;
  width: 100%;
  padding: 16px;
  overflow: hidden;

  :global {
    .ant-tabs-nav-wrap {
      height: 56px;
      background-color: #fff;
      padding-left: 30px;
      padding-right: 30px;
      border-bottom: 1px solid #e5e5e5;
    }

    .ant-tabs-nav {
      margin-bottom: 0;
    }
  }

  .rightTemplateContent {
    padding: 0 20px 20px;
    background-color: #fff;
    height: 100%;
    min-height: 300px;
    .title {

      height: 56px;
      padding: 12px 0px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e5e5e5;
      font-size: 18px;
    }
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    
      margin-top: 20px;
      padding-bottom: 10px;
      
      .searchInput {
        width: 300px;
      }
    }

    .table {
      :global {
        .ant-table-cell {
          white-space: nowrap;
        }
      }
    }
  }

  .viewContent {
    padding: 10px 20px;
    height: calc(100% - 56px);
  }
}