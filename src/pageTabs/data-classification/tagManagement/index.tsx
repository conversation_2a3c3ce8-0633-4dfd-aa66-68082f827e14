import React, { useState } from "react";
import classnames from 'classnames';
import { ResizableBox, ResizableProps } from "react-resizable";
import { Layout } from 'antd';
import { useDispatch } from 'src/hook';
import { ErrorBoundary, Iconfont } from 'src/components';
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import IndustryTypeTree from './IndustryTypeTree';
import RightTagContent from './RightTagContent';
import { ClassificationTagContext } from './ClassificationTagContext'
import styles from './index.module.scss';

const ResizableBoxProps: ResizableProps = {
	axis: "x",
	width: 320,
	height: 0,
	minConstraints: [260, 0],
	maxConstraints: [600, 0],
};

export default () => {

	const dispatch = useDispatch();
	const [selectedTreeNodeInfo, setSelectedTreeNodeInfo] = useState<any>(null);
	const [editItemInfo, setEditItemInfo] = useState<any>(null);

	const ResizeHandle = (
		<div className={styles.resizeHandle}>
			<Iconfont type="icon-handle-8"></Iconfont>
		</div>
	);

	return (
		<ClassificationTagContext.Provider
			value={{
				selectedTreeNodeInfo,
				setSelectedTreeNodeInfo,
				editItemInfo,

			}}
		>
			<div className={styles.tagManagement}>
				<ResizableBox
					className={styles.resizableBox}
					handle={ResizeHandle}
					{...ResizableBoxProps}
				>
					<div className={classnames(styles.leftWrap)}>
						<ErrorBoundary>
							<IndustryTypeTree />
						</ErrorBoundary>
					</div>
				</ResizableBox>
				<div className={styles.rightWrap}>
					<ErrorBoundary>
						<RightTagContent/>
					</ErrorBoundary>
				</div>
			</div>
		</ClassificationTagContext.Provider>
	)
}