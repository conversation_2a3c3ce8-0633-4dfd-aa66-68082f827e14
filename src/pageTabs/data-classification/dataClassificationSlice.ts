import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { TemplateTab, ClassificationTab } from './types';

interface IState {     
  dataClassificationState: any  // 分级分类跳转相关信息存储
  activeTemplateTab: TemplateTab
  selectedTemplateNodeInfo: any
  classActiveTaKey: ClassificationTab
}

const initialState: IState = {
  classActiveTaKey: 'templateManagement', // 分级分类 当前tab
  dataClassificationState: {}, //{ activeTabKey: 当前tab}
  activeTemplateTab: 'builtInTemplate', // 模板管理 当前tab
  selectedTemplateNodeInfo: null, // 模板管理 当前选中tree节点
}

export const dataClassificationSlice = createSlice({
  name: 'dataClassification',
  initialState,
  reducers: {
    setClassActiveTaKey: (state, action: PayloadAction<ClassificationTab>) => {
      state.classActiveTaKey = action.payload
    },
    //模板管理 当前模板类型
    setActiveTemplateTab: (state, action: PayloadAction<TemplateTab>) => {
      state.activeTemplateTab = action.payload
    },
    //模板管理 当前选中节点
    setSelectedTemplateNodeInfo: (state, action: PayloadAction<any>) => {
      state.selectedTemplateNodeInfo = action.payload;
    },
    setDataClassificationState: (state, action: PayloadAction<any>) => {
      state.dataClassificationState = action.payload
    },
  },
})

export const dataClassificationReducer = dataClassificationSlice.reducer

export const { 
  setClassActiveTaKey,
  setDataClassificationState,
  setActiveTemplateTab,
  setSelectedTemplateNodeInfo
} = dataClassificationSlice.actions
