//处理分页limit值
export const getTablePaginatinLimitFiled = (pageNumber: number = 1, pageSize: number = 10) => {
  if (pageNumber === 1 && pageSize === 10) return 10;
  const pre = (pageNumber - 1) * pageSize;
  return `${pre}|${pageSize}`;
}

//['']
export const formattedSelectOptions = (data: string[]) => {
  if (!data?.length) return [];
  return data?.map(item => ({
    label: item,
    value: item
  }))
}
// [[],[]]
export const transformSelectOtionData = (data: string[][]) => {
  if (!data?.length) return [];
  return data.map(item => ({
    label: item[0], // 使用子数组的第一个元素作为 label
    value: item[0]  // 使用子数组的第一个元素作为 value
  }));
}
// data: [['1'], ['3']] index: [1, 2]
export const generateKeyValueObjects = (data: string[][], index: number[]) => {
  if (!data?.length || !index?.length) return [];
  return data.map((item: string[], i: number) => ({
    label: item[0],
    value: index[i]
  }));
}
//data: [['1'], ['3']] index: [1, 2]  => {1: '1', 2: '3'}
export const createMapFromFieldArrays = (valuesArr: string[][], indexArr: number[]) => {
  // 保持两组数据长度一致
  if (!Array.isArray(indexArr) || !Array.isArray(valuesArr)) {
    throw new Error();
  }
  if (indexArr.length !== indexArr.length) {
    throw new Error();
  }
  if (valuesArr.some(item => !Array.isArray(item) || item?.length === 0)) {
    throw new Error();
  }
  // 构建Map 做映射
  const map = new Map<any, any>();
  indexArr.forEach((key, index) => {
    map.set(key, valuesArr?.[index]?.[0]);
  });
  return map;
}

//处理keyMap 转 option
export const formatKeyMapToOption = <T extends Record<string, any>>(obj: T) => {
  return Object.keys(obj).map((key) => ({
    label: obj[key], // 对象的值作为 label
    value: Number(key), // 对象的键转换为数字作为 value
  }));
};