import React, { useState, useMemo } from 'react';
import { Table, Space, Button } from 'antd'
import i18n from 'i18next';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import { useRequest, useSelector } from 'src/hook';
import {
  getClassBuiltTempBindTag
} from 'src/api';
import {
  TagTransferModal
} from './modals';
import styles from './index.module.scss';

interface TagItem {
  key: string;
  name: string;
  level: string;
  category: string;
}

const TagManagementTable = ({
  setGradeConfigModalVisible,
  onRefreshTree
}: {
  setGradeConfigModalVisible: (visible: boolean) => void;
  onRefreshTree?: () => void;
}) => {

  const { t } = useTranslation();
  const { selectedTemplateNodeInfo, activeTemplateTab } = useSelector(state => state.dataClassification);

  //标签
  const [tagModalVisible, setTagModalVisible] = useState<boolean>(false);
  
  // 使用allDescendants数据格式化为表格数据
  const tableData = useMemo(() => {
    // 如果选中的是leaf节点（最后一个级别，有label_names），需要拆分label_names展示
    if (selectedTemplateNodeInfo?.label_names &&
        selectedTemplateNodeInfo?.label_names.trim() !== '' &&
        selectedTemplateNodeInfo?.label_ids &&
        selectedTemplateNodeInfo?.label_ids.trim() !== '') {

      // 拆分label_names和label_ids
      const labelNames = selectedTemplateNodeInfo.label_names.split(',').map((name: string) => name.trim());
      const labelIds = selectedTemplateNodeInfo.label_ids.split('&').map((id: string) => id.trim());

      console.log(selectedTemplateNodeInfo, 'labelNames', labelNames);
      // 为每个标签创建一行数据
      return labelNames.map((labelName: string, index: number) => ({
        id: `${selectedTemplateNodeInfo.id}_${index}`,
        key: `${selectedTemplateNodeInfo.id}_${index}`,
        label_names: labelName,
        classify_name: selectedTemplateNodeInfo.classify_name,
        level: selectedTemplateNodeInfo.level_name,
        label_id: labelIds[index] || '',
        tp_id: selectedTemplateNodeInfo.tp_id,
        parent_id: selectedTemplateNodeInfo.parent_id,
        level_id: selectedTemplateNodeInfo.level_id
      }));
    }

    // 否则使用allDescendants数据（非leaf节点）
    return selectedTemplateNodeInfo?.allDescendants?.map((item: any) => ({
      ...item,
      key: item.id,
      level: item.level_name,
    })) || [];
  }, [selectedTemplateNodeInfo]);
  
  //绑定标签
  const { run: runBindTags } = useRequest(getClassBuiltTempBindTag, {
    manual: true,
    onSuccess: () => {
      setTagModalVisible(false);
      // 绑定标签成功后，刷新树数据以更新右侧显示
      if (onRefreshTree) {
        onRefreshTree();
      }
    }
  });

  // 移除API调用的useEffect，直接使用allDescendants数据


  return (
    <div className={styles.rightTemplateContent}>
      <div className={styles.header}>
        <div className={styles.title}>{selectedTemplateNodeInfo?.title}</div>
        <Space>
          {/* 只在真正的叶子节点显示标签和等级配置按钮 */}
          {
            (
              // 条件1: 真正的叶子节点（无法再展开）
              selectedTemplateNodeInfo?.isLeaf === true ||
              // 条件2: 已经绑定了标签的最终节点
              (selectedTemplateNodeInfo?.label_names && 
               selectedTemplateNodeInfo?.label_names.trim() !== '')
            ) &&
            <>
              <Button type='primary' onClick={() => setTagModalVisible(true)}>{t('classGrading.tab.tag.tagName')}</Button>
              <Button type='primary' onClick={() => setGradeConfigModalVisible(true)}>{t('classGrading.tab.tag.gradeConfig')}</Button>
            </>
          }
        </Space>
      </div>
      <div className={styles.tableContainer}>
        <Table
          rowKey="key"
          columns={columns}
          loading={!selectedTemplateNodeInfo?.allDescendants && selectedTemplateNodeInfo?.tp_id}
          dataSource={tableData}
          scroll={{ y: 400 }}  //固定表格内容区域高度
          pagination={{
            showQuickJumper: true,
            showSizeChanger: true,
            total: tableData?.length || 0,
            showTotal: (total) => t('common.table.pagination.total', { total }),
          }}
        />
      </div>

      {/* 标签 */}
      {
        tagModalVisible &&
        <TagTransferModal
          activeTemplateTab={activeTemplateTab}
          onCancel={() => { setTagModalVisible(false); }}
          onSubmit={(params: any) => {
            // 准备绑定标签的参数
            const bindParams = {
              id: Number(selectedTemplateNodeInfo?.id), // 使用左边选中节点的id，转为int类型
              label_ids: params.label_ids?.map((id: string) => Number(id)) || [], // 转为数字数组
            };

            runBindTags(bindParams);
          }}
        />
      }
    </div>
  );
};

export default TagManagementTable;

const columns: ColumnsType<TagItem> = [
  {
    title: i18n.t('classGrading.tab.tag.column.tagName'),
    dataIndex: 'label_names',
    key: 'label_names',
    width: '40%',
    ellipsis: true
  },
  {
    title: i18n.t('classGrading.tab.tag.level'),
    dataIndex: 'level',
    key: 'level',
    width: '20%',
    ellipsis: true
  },
  {
    title: i18n.t('classGrading.tab.tag.category'),
    dataIndex: 'classify_name',
    key: 'classify_name',
    width: '40%',
    ellipsis: true

  },
];