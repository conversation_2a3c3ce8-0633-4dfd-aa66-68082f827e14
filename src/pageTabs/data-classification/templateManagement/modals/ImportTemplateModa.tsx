import React, {  useState } from 'react';
import { Form, Input, Select, Upload, Button } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import type { UploadProps } from 'antd/es/upload';
import { Iconfont, UIModal } from 'src/components';
import { useRequest } from 'src/hook';
import {
  QueryStorageAPI,
  QueryLabelTypeAPI,
  QueryTemplateSelectLvAPI,
  QueryDataSourceAPI,
  ClassFileUploadAPI,
  ClassTagDownloadExampleAPI,
  ClassTemplateImportAPI
} from 'src/api';
import { FormLayout } from '../../constants';
import { useTranslation } from 'react-i18next';

const ImportTemplateModal = ({
  onCancel
}: {
  onCancel: () => void;
}) => {

  const { t } = useTranslation();
  const [importTemForm] = Form.useForm();

  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const { loading: uploadLoading, run: runFileUpload } = useRequest(
    (params: { jUploaderFile: File; filetype: string }) => ClassFileUploadAPI(params),
    {
      manual: true,
      onSuccess(res) {
        console.log(t('common.msg.uploadSuccess'), res);
      },
      onError(error) {
        console.error(t('common.msg.uploadFailed'), error);
      }
    }
  )

  const [submitBtnLoading, setSubmitBtnLoading] = useState(false);

  const { run: runClassTagDownloadExampleAPI } = useRequest(ClassTagDownloadExampleAPI, {
    manual: true,
    onSuccess(res) {
      console.log(t('common.msg.downloadSuccess'), res);
    },
  })

  const onDownloadExample = (filename: string) => {
    runClassTagDownloadExampleAPI({ filename })
  }

  const handleOk = async () => {
    try {
      const values = await importTemForm.validateFields();
      setSubmitBtnLoading(true);
      
      console.log('表单值：', values);
      console.log('关联方式值：', values.connectionMethod);
      
      // 处理关联数据源：如果是数组则用逗号连接，否则转为字符串
      const dataSourceValue = Array.isArray(values.dataSource) 
        ? values.dataSource.join(',') 
        : (values.dataSource !== undefined ? values.dataSource.toString() : '');
      
      // 如果选择了自定义模板，使用用户输入的分级名称，否则使用空字符串
      const finalName = values.relatedTemplate === 0 && values.gradeName 
        ? values.gradeName 
        : '';

      // 文件在选择时已经自动上传，这里不需要再上传

      // 构建表单数据（不包含文件数据）
      const jsonData = {
        tp_name: values.templateName || '',
        type: values.connectionMethod !== undefined ? values.connectionMethod.toString() : '',
        type_ds: dataSourceValue,
        type_bq: values.tagType !== undefined ? values.tagType.toString() : '',
        fj_select: values.relatedTemplate !== undefined ? values.relatedTemplate.toString() : '',
        fj_name: finalName,
        file_name: fileList[0]?.name || '',
        action: 'import_template'
      };

      // 提交表单数据
      await ClassTemplateImportAPI(jsonData);
      console.log(t('common.msg.importSuccess'));
      onCancel(); // 关闭弹窗
    } catch (error) {
      console.log(t('common.msg.importFailed'), error);
    } finally {
      setSubmitBtnLoading(false);
    }
  };

  const uploadProps: UploadProps = {
    name: 'jUploaderFile',
    customRequest: async ({ file, onSuccess, onError }) => {
      try {
        // 上传文件 - 直接传递 File 对象，使用 multipart/form-data
        await runFileUpload({
          jUploaderFile: file as File,
          filetype: 'data'
        });
        
        if (onSuccess) {
          onSuccess({} as any, {} as any);
        }
      } catch (error) {
        onError?.(error as Error);
      }
    },
    beforeUpload: (file) => {
      setFileList([file]);
      return true; // 允许自动上传
    },
    onRemove: () => {
      setFileList([]);
    },
    onChange: ({ file }) => {
      console.log(file, 'info')
      setFileList([file]);
    },
    fileList,
    multiple: false
  };


  // 查询关联方式
  const { data: connectionMethodOptions } = useRequest(
    () => QueryStorageAPI({ key: 'dd:data_dams_classifys_into_type' }),
    {
      formatResult(res: any) {
        const datasArray = res?.datas?.data || [];
        const indexArray = res?.datas?.index || [];
        return datasArray.map((item: any[], arrayIndex: number) => ({
          label: item[0], // 取数组第一个元素作为标签
          value: indexArray[arrayIndex], // 使用API返回的真实索引值
        }));
      },
    }
  );

  // 查找标签类型列表
  const { data: tagTypeOptions } = useRequest(QueryLabelTypeAPI, {
    formatResult(res: any) {
      const datasArray = res?.datas?.data || [];
      const indexArray = res?.datas?.index || [];
      return datasArray.map((item: any[], arrayIndex: number) => ({
        label: item[0], // 取数组第一个元素作为标签
        value: indexArray[arrayIndex], // 使用API返回的真实索引值
      }));
    },
  });

  // 查询关联分级模板列表
  const { data: templateOptions } = useRequest(QueryTemplateSelectLvAPI, {
    formatResult(res: any) {
      const datasArray = res?.datas?.data || [];
      const indexArray = res?.datas?.index || [];
      return datasArray.map((item: any[], arrayIndex: number) => ({
        label: item[0], // 取数组第一个元素作为标签名称
        value: indexArray[arrayIndex], // 使用API返回的真实索引值
      }));
    },
  });

  // 查询关联数据源
  const { data: dataSourceOptions } = useRequest(QueryDataSourceAPI, {
    formatResult(res: any) {
      const datasArray = res?.datas?.data || [];
      const indexArray = res?.datas?.index || [];
      return datasArray.map((item: any[], arrayIndex: number) => ({
        label: item[0], // 取数组第一个元素作为标签
        value: indexArray[arrayIndex], // 使用API返回的真实索引值
      }));
    },
  });

  return (
    <UIModal
      title={t('common.btn.import')}
      visible={true}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={submitBtnLoading}
      width={600}
      destroyOnClose
    >
      <Form form={importTemForm} {...FormLayout}>
        <Form.Item
          label={t('classGrading.tab.template.templateName.label')}
          name="templateName"
          rules={[{ required: true, message: t('classGrading.tab.template.templateName.plac') }]}
        >
          <Input placeholder={t('classGrading.tab.template.templateName.plac')} />
        </Form.Item>
        <Form.Item
          label={t('classGrading.tab.template.connMethod.label')}
          name="connectionMethod"
          rules={[{ required: true, message: t('classGrading.tab.template.connMethod.plac') }]}
        >
          <Select options={connectionMethodOptions} placeholder={t('classGrading.tab.template.connMethod.plac')} allowClear />
        </Form.Item>

        <Form.Item noStyle dependencies={['connectionMethod']}>
          {
            ({ getFieldValue }) => {
              const connectionMethodValue = getFieldValue('connectionMethod');
              const selectedOption = connectionMethodOptions?.find((option: any) => option.value === connectionMethodValue);
              const isDataSource = selectedOption?.label?.includes('数据源');
              
              return isDataSource && (
                <Form.Item
                  label={t('classGrading.tab.template.dataSource.label')}
                  name="dataSource"
                  rules={[{ required: true, message: t('classGrading.tab.template.dataSource.plac') }]}
                >
                  <Select mode="multiple" options={dataSourceOptions} placeholder={t('classGrading.tab.template.dataSource.plac')} allowClear />
                </Form.Item>
              )
            }
          }
        </Form.Item>
        <Form.Item
          label={t('classGrading.tab.template.tagType.label')}
          name="tagType"
          rules={[{ required: true, message: t('classGrading.tab.template.tagType.plac') }]}
        >
          <Select options={tagTypeOptions} placeholder={t('classGrading.tab.template.tagType.plac')} allowClear />
        </Form.Item>

        <Form.Item
          label={t('classGrading.tab.template.relatedTemplate.label')}
          name="relatedTemplate"
          rules={[{ required: true, message: t('classGrading.tab.template.relatedTemplate.plac') }]}
        >
          <Select options={templateOptions} placeholder={t('classGrading.tab.template.relatedTemplate.plac')} allowClear />
        </Form.Item>

        <Form.Item noStyle dependencies={['relatedTemplate']}>
          {
            ({ getFieldValue }) => {
              const isCustomTemplate = getFieldValue('relatedTemplate') === 0; // 0 是"自定义"的索引值
              return isCustomTemplate && (
                <Form.Item
                  label={t('classGrading.tab.template.gradeName.label')}
                  name="gradeName"
                  rules={[{ required: true, message: t('classGrading.tab.template.gradeName.plac') }]}
                >
                  <Input placeholder={t('classGrading.tab.template.gradeName.defaultText')} />
                </Form.Item>
              )
            }
          }
        </Form.Item>

        <Form.Item
          label={t('common.downloadTemplate.fileUpload.upload')}
          name='file'
          rules={[{ required: true, message: t('classGrading.tab.tag.file.plac') }]}
        >
          <Upload {...uploadProps}>
            <Button type='primary' icon={<UploadOutlined />}>{t('classGrading.tab.tag.file.btnText')}</Button>
          </Upload>
        </Form.Item>

        <Form.Item label={t('classGrading.tab.template.action.importTemplate')}>
          <div>
            <Button type='text' onClick={() => onDownloadExample(t('classGrading.tab.template.example.relatedTag'))}>
              <Iconfont type='icon-wenjianjia' className='ml10' />{t('classGrading.tab.template.example.relatedTag')}
            </Button>
            <br />
            <Button type='text' onClick={() => onDownloadExample(t('classGrading.tab.template.example.relatedDataSource'))}>
              <Iconfont type='icon-wenjianjia' className='ml10' />{t('classGrading.tab.template.example.relatedDataSource')}
            </Button>
          </div>
        </Form.Item>
      </Form>
    </UIModal>
  );
}

export default ImportTemplateModal;
