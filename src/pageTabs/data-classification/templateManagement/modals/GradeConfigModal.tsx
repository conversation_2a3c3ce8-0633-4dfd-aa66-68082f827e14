import React, { useEffect } from 'react';
import { Form, Cascader, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { useRequest, useSelector } from 'src/hook';
import { UIModal } from 'src/components';
import {
  getClassBindCls,
  getClassDatasourceAPI
} from 'src/api';
interface TemplateFormValues {
  templateName: string;
  tagType?: string;
  relatedTemplate?: string;
}

interface ClassifyLevelResponse {
  code: number;
  message: string;
  datas: {
    columns: string[];
    index: number[];
    data: [string, string][];
  };
}

const GradeConfigModal = ({
  onCancel,
  onRefreshTree
}: {
  onCancel: () => void;
  onRefreshTree?: () => void;
}) => {

  const {t }= useTranslation();
  const [templateForm] = Form.useForm<TemplateFormValues>();

  // 获取当前选中的节点信息
  const { selectedTemplateNodeInfo } = useSelector(state => state.dataClassification);

  //绑定分级
  const {loading ,run: runBindClsLevel } = useRequest(getClassBindCls, {
    manual: true,
    onSuccess: () => {
      console.log('等级配置绑定成功，准备刷新树结构');
      message.success('绑定成功')
      onCancel();
      // 绑定等级配置成功后，刷新树数据以更新右侧显示
      if (onRefreshTree) {
        console.log('调用 onRefreshTree 刷新树结构');
        onRefreshTree();
      } else {
        console.log('onRefreshTree 函数不存在');
      }
    }
  })
  const { data: levelOptions, run: runGetClassifyLevel } = useRequest(getClassDatasourceAPI,{
    manual: true,
    formatResult: (res: ClassifyLevelResponse) => {
      console.log(res)
      console.log(res?.datas?.data,'demo')
      if (res?.datas?.data && res?.datas?.index) {
        // 将扁平数据转换为树形结构，同时保存index映射
        const groupedData: Record<string, Array<{levelName: string, indexValue: number}>> = {};
        
        res.datas.data.forEach(([tpName, levelName]: [string, string], dataIndex: number) => {
          const indexValue = res.datas.index[dataIndex];
          if (!groupedData[tpName]) {
            groupedData[tpName] = [];
          }
          groupedData[tpName].push({ levelName, indexValue });
        });

        // 转换为Cascader需要的格式
        return Object.entries(groupedData).map(([tpName, levels]) => ({
          label: tpName,
          value: tpName,
          children: levels.map(({ levelName, indexValue }) => ({
            label: levelName,
            value: indexValue
          }))
        }));
      }
      return [];
    }
  })

  useEffect(() => {
    runGetClassifyLevel({key: 'dd:data_dams_classify_level'})
  }, [runGetClassifyLevel])
  
  const onSubmit = () => {
    templateForm.validateFields().then((values) => {
      // Cascader返回的是[parentValue, childValue]数组，我们需要childValue作为level_id
      const levelId = Array.isArray(values.templateName)
        ? values.templateName[values.templateName.length - 1]
        : values.templateName;

      // 使用新的API参数格式
      runBindClsLevel({
        id: Number(selectedTemplateNodeInfo?.id || 0), // integer类型
        level_id: Number(levelId) // integer类型
      })
    })
  };

  return (
    <UIModal
      title={t('classGrading.tab.tag.gradeConfig')}
      visible={true}
      onOk={onSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      width={600}
    >
      <Form form={templateForm}>
        <Form.Item
          label={t('classGrading.tab.template.level.label')}
          name="templateName"
        >
          <Cascader 
            options={levelOptions} 
            placeholder={t('classGrading.tab.template.level.plac')}
            showSearch
            changeOnSelect={true}
            expandTrigger="hover"
            popupClassName="grade-cascader-popup"
          />
        </Form.Item>
      </Form>
    </UIModal>
  );
}

export default GradeConfigModal;