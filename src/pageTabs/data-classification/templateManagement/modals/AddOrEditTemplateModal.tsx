import React, { useEffect } from 'react';
import { Form, Input, Select } from 'antd';
import type { FormInstance } from 'antd/es/form';
import { useTranslation } from 'react-i18next';
import { useRequest } from 'src/hook';
import {
  getClassDatasourceAPI
} from 'src/api';
import { UIModal } from 'src/components';
import { FormLayout } from '../../constants';
import { generateKeyValueObjects } from '../../utils';

export default ({
  detailInfo,
  onCancel,
  onSubmit
}: {
  detailInfo: any;
  onCancel: () => void;
  onSubmit: (values: any) => void;
}) => {

  const { t } = useTranslation();
  const [templateForm] = Form.useForm();

  const { loading: submitBtnLoading, run: Update } = useRequest('', {

  })
  //查询标签关联类型
  const {data: tagList, run: runGetTagType } = useRequest(getClassDatasourceAPI, {
    manual: true,
    formatResult(res: any) {
      if (!res?.datas?.data?.length || !res?.datas?.index) return [];
      return generateKeyValueObjects(res.datas.data, res.datas.index);
    },
  })

   //查询关联分级分类列表
   const {data: classifyList, run: runGetClassifyType } = useRequest(getClassDatasourceAPI, {
    manual: true,
    formatResult(res: any) {
      if (!res?.datas?.data?.length || !res?.datas?.index) return [];
      return generateKeyValueObjects(res.datas.data, res.datas.index);
    },
  })

  useEffect(() => {
    runGetTagType({ key: 'dd:dams_label_type' });
    runGetClassifyType({key: 'dd:data_template_selectlv'})
  }, [])

  useEffect(() => {
    if (!templateForm) return;
    // 设置表单初始值，将API返回的id字段映射到表单字段
    templateForm.setFieldsValue({
      tp_name: detailInfo?.tp_name,
      label_type: detailInfo?.label_type_id, // 使用label_type_id作为表单值
      level_type: detailInfo?.level_type_id, // 使用level_type_id作为表单值
    })
  }, [templateForm, detailInfo]);

  const onFormValidate = () => {
    templateForm.validateFields().then((values) => {
      // 准备API参数，确保所有字段都有值（空值用空字符串）
      const apiParams = {
        action: detailInfo ? 'edit_template' : 'create_template',
        tp_name: values.tp_name || '',
        tp_id: detailInfo?.tp_id ? String(detailInfo.tp_id) : '',
        subclass_count: detailInfo?.subclass_count || '',
        label_count: detailInfo?.label_count || '',
        // 编辑时：使用表单选择的值；新增时：使用表单选择的值
        label_type: values.label_type ? String(values.label_type) : '',
        level_type: values.level_type ? String(values.level_type) : '',
      };

      console.log('提交模板参数:', apiParams);
      onSubmit(apiParams);
    })
  };

  return (
    <UIModal
      title={detailInfo ? t('classGrading.tab.template.editTemplate') : t('classGrading.tab.template.addTemplate')}
      visible={true}
      onOk={onFormValidate}
      onCancel={onCancel}
      confirmLoading={submitBtnLoading}
      width={600}
    >
      <Form form={templateForm} {...FormLayout}>
        <Form.Item
          label={t('classGrading.tab.template.templateName.label')}
          name="tp_name"
          rules={[{ required: true, message: t('classGrading.tab.template.templateName.plac') }]}
        >
          <Input placeholder={t('classGrading.tab.template.templateName.plac')} />
        </Form.Item>

        <Form.Item
          label={t('classGrading.tab.template.tagType.label')}
          name="label_type"
          >
          <Select options={tagList} placeholder={t('classGrading.tab.template.tagType.plac')} allowClear />
        </Form.Item>

        <Form.Item
          label={t('classGrading.tab.template.relatedTemplate.label')}
          name="level_type"
          >
          <Select options={classifyList} placeholder={t('classGrading.tab.template.relatedTemplate.plac')} allowClear />
        </Form.Item>
      </Form>
    </UIModal>
  );
}
