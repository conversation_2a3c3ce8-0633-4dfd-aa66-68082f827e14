import React, { useState, useEffect, useMemo } from 'react';
import { Tree, Button, Input } from 'antd';
import { useTranslation } from 'react-i18next';
import { DeleteOutlined, ArrowRightOutlined, DownOutlined } from '@ant-design/icons';
import { useRequest } from 'src/hook';
import { UIModal } from 'src/components';
import { filterNodesNotMatch } from 'src/util';
import {
  getClassDatasourceAPI,
  QueryTagListAPI
} from 'src/api';
import { TemplateTab } from '../../types';
import styles from './index.module.scss';

interface TreeNode {
  key: string;
  title: string;
  children?: TreeNode[];
  isLeaf?: boolean;
  level?: number;
  parentKey?: string;
  name?: string;
  type?: string;
  label_id?: string | number;
  // 添加来自API的其他字段
  id?: number;
  label_name?: string;
  threshold?: number;
  data_type?: number;
  personal_bool?: number;
  profession_bool?: number;
}

const TagTransferModal = ({
  activeTemplateTab,
  onCancel,
  onSubmit
}: {
  activeTemplateTab: TemplateTab,
  onCancel: () => void;
  onSubmit: (data: any) => void;
}) => {
  const { t } = useTranslation();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [listData, setListData] = useState<TreeNode[]>([]);
  const [searchValue, setSearchValue] = useState<string | null>(null);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(false);
  const [loadedKeys, setLoadedKeys] = useState<React.Key[]>([]); // 记录已加载的节点
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [transferLoading, setTransferLoading] = useState<boolean>(false); // 传输按钮加载状态

  // 获取行业类型列表（第一层数据）
  const { run: runGetIndustryTypes } = useRequest(getClassDatasourceAPI, {
    manual: true,
    formatResult(res: any) {
      const datasObj = res?.datas || {};
      const { data = [], index = [] } = datasObj;
      
      return data.map((item: any, idx: number) => ({
        key: `industry_${index[idx]}`,
        title: item[0],
        name: item[0],
        level: 0,
        type: index[idx].toString(),
        isLeaf: false, // 行业类型默认不是叶子节点
        children: []
      }));
    },
    onSuccess: (data) => {
      setTreeData(data);
      setFilteredTreeData(data);
    }
  });

  // 格式化子标签数据
  const formatSubTagsData = (res: any) => {
    // 检查是否是第二层API的数据格式（onLoadData返回的格式）
    console.log('data', res?.datas)
    if (res?.datas && Array.isArray(res.datas)) {
      const { datas } = res;
      
      if (!datas || datas.length === 0) {
        return [];
      }
      
      return datas.map((item: any, idx: number) => ({
        key: `tag_${item.label_id || item.id}_${Date.now()}_${idx}`,
        title: item.label_name || item.name || `标签_${idx}`,
        name: item.label_name || item.name,
        level: 1,
        isLeaf: true,
        label_id: item.label_id || item.id,
        // 保留原始数据以备后用
        ...item
      }));
    }
    
    // 保持原有的第一层数据处理逻辑作为fallback
    const datasObj = res?.data?.datas || {};
    const { data = [], index = [] } = datasObj;
    
    if (!data || data.length === 0) {
      return [];
    }
    
    return data.map((item: any, idx: number) => ({
      key: `tag_${index[idx]}_${Date.now()}_${idx}`,
      title: item[0] || item.name || `标签_${idx}`,
      name: item[0] || item.name,
      level: 1,
      isLeaf: true,
    }));
  };

  const [filteredTreeData, setFilteredTreeData] = useState<TreeNode[]>([]);

  useEffect(() => {
    runGetIndustryTypes({ key: 'dd:dams_label_type' });
  }, [activeTemplateTab, runGetIndustryTypes]);

  // 动态加载子节点
  const onLoadData = async (treeNode: any): Promise<void> => {
    const { key, name, type } = treeNode;
    
    // 如果已经加载过或者是叶子节点，直接返回
    if (loadedKeys.includes(key) || treeNode.isLeaf) {
      return Promise.resolve();
    }

    try {
      const response = await QueryTagListAPI({
        action: 'query_tag_list',
        label_name: '',
        type: type,
        limit: '10000',
        label_id: ''
      });
      
      const subTags = formatSubTagsData(response);

      // 更新树形数据
      const updateTreeData = (nodes: TreeNode[]): TreeNode[] => {
        return nodes.map(node => {
          if (node.key === key) {
            // 如果没有子数据，标记为叶子节点
            if (subTags.length === 0) {
              return { ...node, isLeaf: true, children: [] };
            }
            return { ...node, children: subTags, isLeaf: false };
          }
          if (node.children && node.children.length > 0) {
            return { ...node, children: updateTreeData(node.children) };
          }
          return node;
        });
      };

      const newTreeData = updateTreeData(treeData);
      setTreeData(newTreeData);
      setFilteredTreeData(searchValue ? filterNodesNotMatch(newTreeData, searchValue) : newTreeData);
      setLoadedKeys(prev => [...prev, key]);
    } catch (error) {
    }
  };

  // 判断节点是否为叶子节点
  const isLeafNode = (node: any) => {
    return node.isLeaf || (!node.children || node.children.length === 0);
  };

  // 递归查找叶子节点的 key
  const getLeafKeys = (node: any) => {
    const leafKeys: React.Key[] = [];
    if (isLeafNode(node)) {
      leafKeys.push(node.key);
    } else {
      node.children.forEach((child: any) => {
        leafKeys.push(...getLeafKeys(child));
      });
    }
    return leafKeys;
  };

  const handleTreeSelect = (_keys: any, info: any) => {
    // 仅处理子节点的选中状态
    const selectedNode = info.node;
    const leafKeys = getLeafKeys(selectedNode); 
  
    let newSelectedKeys = [...selectedKeys];
    if (info.checked) {
      newSelectedKeys = [...newSelectedKeys, ...leafKeys];
    } else {
      newSelectedKeys = newSelectedKeys.filter((key) => !leafKeys.includes(key));
    }
    setSelectedKeys(newSelectedKeys);
  };

  // 处理箭头按钮点击事件
  const handleArrowClick = async () => {
    setTransferLoading(true);
    const newListData: TreeNode[] = [...listData];
    const processedKeys = new Set(); // 用于记录已处理的节点 key

    try {
      // 遍历选中的节点
      for (const key of selectedKeys) {
        const node = findNodeByKey(filteredTreeData, key);
        if (node && !processedKeys.has(key)) {
          // 检查是否为未展开的父级节点（第一层行业类型）
          if (node.level === 0 && (!node.children || node.children.length === 0)) {
            try {
              // 调用API加载子数据
              const response = await QueryTagListAPI({
                action: 'query_tag_list',
                label_name: '',
                type: node.type,
                limit: '10000',
                label_id: ''
              });
              
              const subTags = formatSubTagsData(response);
              // 将子标签添加到列表
              subTags.forEach((child: any) => {
                if (!processedKeys.has(child.key)) {
                  newListData.push(child);
                  processedKeys.add(child.key);
                }
              });
            } catch (error) {
              // 如果API调用失败，直接添加父节点
              newListData.push(node);
              processedKeys.add(key);
            }
          } else if (node?.children && node.children.length > 0) {
            // 处理已展开的父节点，添加其子节点
            node.children.forEach((child: any) => {
              if (!processedKeys.has(child.key)) {
                newListData.push(child);
                processedKeys.add(child.key);
              }
            });
          } else {
            // 如果是叶子节点，直接添加
            newListData.push(node);
            processedKeys.add(key);
          }
        }
      }
    
    // 去重并更新列表
    const uniqueArray = newListData.reduce((acc: any[], item: any) => {
      const isDuplicate = acc.some((obj: any) => obj.key === item.key);
      if (!isDuplicate) {
        acc.push(item);
      }
      return acc;
    }, []);
    setListData(uniqueArray);
    } finally {
      setTransferLoading(false);
    }
  };

  // 删除事件
  const handleDelete = (key: React.Key) => {
    // 过滤掉被删除的节点
    const newListData = listData.filter((item) => item.key !== key);
    setListData(newListData);
    // 更新左侧 Tree 的选中状态
    let newSelectedKeys = selectedKeys.filter((selectedKey) => selectedKey !== key);
    // 移除父节点的 key
    const parent = findParentByKey(treeData, key);
    if (parent) {
      newSelectedKeys = newSelectedKeys.filter((selectedKey) => selectedKey !== parent.key);
    }
  
    setSelectedKeys(newSelectedKeys);
  };

  // 根据 key 查找节点
  const findNodeByKey = (data: TreeNode[], key: React.Key): TreeNode | null => {
    for (const node of data) {
      if (node.key === key) {
        return node;
      }
      if (node?.children?.length) {
        const result = findNodeByKey(node.children, key);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  // 递归查找父节点
  const findParentByKey = (data: TreeNode[], key: React.Key): any => {
    for (const node of data) {
      if (node?.children?.length) {
        const child = node.children.find((child) => child.key === key);
        if (child) {
          return node;
        }
        const result: TreeNode | null = findParentByKey(node.children, key);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  useEffect(() => {
    setFilteredTreeData(searchValue ? filterNodesNotMatch(treeData, searchValue) : treeData);
  }, [searchValue, treeData]);


  function getExpandKeysNodeTreeAllChildren(treeNode: any) {
    const keys: string[] = []

    const getKeys = (node: any) => {
      if (node?.children?.length) keys.push(node.key)
      if (node?.children?.length) {
        node.children.forEach((child: any) => getKeys(child))
      }
    }
    getKeys(treeNode)

    return keys
  }

  useEffect(() => {
    if (!searchValue && treeData.length > 0) {
      // 删除搜索内容后，保留选中的信息
      setSelectedKeys(prevSelectedKeys => 
        prevSelectedKeys.filter((key) => {
          const node = findNodeByKey(treeData, key);
          return node !== null;
        })
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchValue, treeData]);

  useEffect(() => {
    if (searchValue && filteredTreeData) {
      let keys: string[] = [];

      filteredTreeData?.forEach((item: any) => {
        keys = keys.concat(getExpandKeysNodeTreeAllChildren(item))
      })

      setExpandedKeys(keys)
    }
  }, [searchValue, filteredTreeData])

  const handleExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys)
    setAutoExpandParent(false)
  }
  
  const onSubmitSelectedTags = () => {
     // 收集选中标签的label_id
     const labelIds = listData
       .filter((item: any) => item.label_id) // 只取有label_id的数据
       .map((item: any) => String(item.label_id)); // 转为字符串数组

     onSubmit({label_ids: labelIds})
  }


  return (
    <UIModal
      visible={true}
      title={t('classGrading.tab.tag.tagName')}
      onCancel={onCancel}
      onOk={onSubmitSelectedTags}
      className={styles.tagModal}
    >
      <div className='flexAlignCenter'>
        <div className={styles.leftTree}>
          <Input placeholder={t('common.search.input.placeholder')} onChange={(e) => setSearchValue(e.target.value)} />
          <Tree.DirectoryTree
            checkable
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            checkStrictly={false}
            onCheck={handleTreeSelect}
            onExpand={handleExpand}
            loadData={onLoadData}
            treeData={filteredTreeData}
            checkedKeys={selectedKeys}
            style={{ width: '300px' }}
          />
        </div>
        {/* 中间箭头按钮 */}
        <Button
          shape="circle"
          icon={<ArrowRightOutlined />}
          className='ml10 mr10'
          loading={transferLoading}
          onClick={handleArrowClick}
          disabled={selectedKeys.length === 0}
        />
        {/* 右侧 List */}
        <div className={styles.rightList}>
          {
            listData?.map(item => (
              <div key={item.key} className={styles.item}>
                <div>{item?.title}</div>
                <Button
                  type='text'
                  icon={<DeleteOutlined className={styles.deleteIcon} />}
                  onClick={() => handleDelete(item.key)}
                />
              </div>
            ))
          }
        </div>
      </div>
    </UIModal>
  );
};

export default TagTransferModal;