import React, { useEffect } from 'react';
import { Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';
import type { FormInstance } from 'antd/es/form';
import { useRequest, useSelector } from 'src/hook';
import { UIModal } from 'src/components';
import {
  getClassDatasourceAPI
} from 'src/api';
import { FormLayout } from '../../constants';

export default ({
  detailInfo,
  onCancel,
  onSubmit
}: {
  detailInfo: any;
  onCancel: () => void;
  onSubmit: (values: any) => void;
}) => {

  const { t } = useTranslation();
  const [classForm] = Form.useForm();

  // 获取当前选中的节点信息
  const { selectedTemplateNodeInfo } = useSelector(state => state.dataClassification);

  useEffect(() => {
    if (!classForm) return;
    classForm.setFieldsValue({
     ...detailInfo
    })
  }, [classForm]);

  const onFormValidate = () => {
    classForm.validateFields().then((values) => {
      let apiParams;

      if (detailInfo) {
        // 编辑分类：使用 /api/edit_classification 接口
        apiParams = {
          id: String(detailInfo.id || 0), // 编辑时使用detailInfo的id
          classify_name: values.label || '', // 分类名称
        };
        console.log('编辑分类参数:', apiParams);
      } else {
        // 新增分类：使用 /api/add_classification 接口，数字类型使用integer
        apiParams = {
          id: Number(selectedTemplateNodeInfo?.id || 0), // 当前选中节点的id，integer类型
          classify_name: values.label || '', // 分类名称，string类型
          tp_id: Number(selectedTemplateNodeInfo?.tp_id || 0), // 模板id，integer类型
          level_id: Number(selectedTemplateNodeInfo?.level_id || 0), // 级别id，integer类型
          // parent_id: Number(selectedTemplateNodeInfo?.parent_id || selectedTemplateNodeInfo?.id || 0), // 父级id，integer类型
          label_ids: '' // 标签ids，string类型
        };
        console.log('新增分类参数:', apiParams);
      }

      onSubmit(apiParams);
    })
  };

  return (
    <UIModal
      title={detailInfo ? t('classGrading.tab.template.editClass') : t('classGrading.tab.template.addClass')}
      visible={true}
      onOk={onFormValidate}
      onCancel={onCancel}
      confirmLoading={false}
      width={600}
    >
      <Form form={classForm} {...FormLayout}>
        <Form.Item
          label={t('classGrading.tab.template.className.label')}
          name="label"
          rules={[{ required: true, message: t('classGrading.tab.template.className.plac') }]}
        >
          <Input placeholder={t('classGrading.tab.template.className.plac')} />
        </Form.Item>
      </Form>
    </UIModal>
  );
}
