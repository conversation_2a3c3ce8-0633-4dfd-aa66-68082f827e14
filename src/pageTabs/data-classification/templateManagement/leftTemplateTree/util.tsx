import React from "react";
import { DataNode } from "antd/lib/tree";
import { Iconfont } from "src/components";
import styles from '../index.module.scss';
import classnames from 'classnames';
import type { ResponseNodeEntity } from "src/types";
import { FlowElementTreeNodeEntity, ClassBuiltTempItem } from "src/api";

// 构造树结构
export const generateTree = (data: any[]) => {
	const allId = data?.map(i => i.id)?.filter((i, index, arr) => arr.indexOf(i) === index);
	const allParentId = data?.map(i => i.parentId)?.filter((i, index, arr) => arr.indexOf(i) === index);

	const validateParentId = (item: any) => {
		return !!data?.filter((i: any) => {
			return i?.parentId === item?.id && item?.nodeType !== i?.nodeType;
		})?.length;
	};

	const filterData = data.filter((item, _, arr) => {
		// 自身是父级（且id和parentId不能重复）
		if (allParentId.includes(item.id) && validateParentId(item)) {
			item.children = arr.filter(i => i.parentId === item.id);
		}
		// 没有父级
		if (!allId.includes(item.parentId) || !item.parentId) {
			return true;
		}
		return false;
	});

	const formatTree = (data: any[], connectionType?: string): any[] =>
		data.map((item: any) => {
			const { nodeName, nodeType, connection, id } = item;
			const icon = `${nodeType === "datasource" ? `icon-connection-${nodeName}` : nodeType === "group" ? "icon-shujukuwenjianjia" : nodeType === "connection" ? `icon-${connection?.connectionType}` : `icon-${nodeType}`} `;
			item.key = item.nodePath; // 构造唯一id
			item.title = item.nodeName;
			item.connectionType = connectionType || id
			item.icon = <Iconfont 
                    type={icon} 
                    className={classnames(styles.mr4, styles.color008dff, {
                      [styles.colorf00]: connection?.testModel === 1 && nodeType === "connection",
                      [styles.colorgreen]: !!connection?.testModel && connection?.testModel !== 1 && nodeType === "connection",
                    })}
                  ></Iconfont>;
			if (item.children) {
				item.children = formatTree(item.children, item.connectionType);
			}
			item.props = { ...item };
			return item;
		});
	return formatTree(filterData);
};

export const getConnExpandNodeNeedPathKeys = (path: string,needSplice?: boolean) => {
  let result = [];
  const parts = path?.split('/') || [];
  let current = '';
  for (let i = 1; i < parts.length; i++) {
    current += '/' + parts[i];
    result.push(current);
  }
  if (needSplice) {
    result.splice(0, 1); // 删除第一个空字符串
  }
 
  return result;
}

export const matchKeyword = (target = '', substring = '') => {
  if (!target) return false
  return target.toLowerCase().indexOf(substring.toLowerCase()) > -1
}

	/**
	 * pass a key to find the relative node
	 */
  export function findNodeByKey(nodes: DataNode[], key: string): DataNode | undefined {
		if (!key || !nodes || !nodes.length) return;

		for (const item of nodes) {
			// find target node
			//@ts-ignore
			if (item?.props?.nodePath === key) {
				return item;
			}

			if (item?.children && item?.children?.length > 0) {
				const res = findNodeByKey(item.children, key);
				if (res) return res;
			}
		}
		return;
	}

// 解析树节点的key，拆分成为各层节点的key的数组
export const analyzeNodeKey = (key: string) => {
	const list: string[] = key.split('/').slice(2); ///去除第一个空的和root
	const keyList: string[] = list.map((_, index: number) => {
		return list.reduce((pre: string, cur: string, curIndex: number) => {
			if (curIndex <= index) return pre + '/' + cur
			else return pre
		}, '/root')
	})
	return keyList
}

// 递归获取指定连接key的节点树
export const recursionGetCNTreeData = (data: any[], key: string) => {
	let result: any[] = []
	data.map((node: any) => {
		if (node.key === key) {
			result = [node]
		}
		else if (node?.children && node?.children?.length > 0) {
			const res = recursionGetCNTreeData(node.children, key)
			if (res && res.length > 0) result = res;
		}
	})
	return result
}



/** 元素节点元信息 */
export type ElementNodeProps = Pick<FlowElementTreeNodeEntity, "connectionId" | "connectionType" | "nodeName" | "nodePath" | "nodeType" | "hasChild"> & {
	nodePathWithType: string;
	groupId: number | null | undefined;
};

// 格式化节点数据
export const formatTreeData = (data: ClassBuiltTempItem[], isRoot?: boolean): DataNode[] => {
	if (!data?.length) return [];

	return data.map((item) => {
		const { tp_id, tp_name } = item;
		return {
			...item,
			key: tp_id,
			value: tp_id,
			title: tp_name,
			isRoot: isRoot
		};
	});
};