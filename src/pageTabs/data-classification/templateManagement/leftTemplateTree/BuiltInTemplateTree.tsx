import React, { useEffect, useState } from "react";
import { DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from "react-i18next";
import { Button, message, Modal, Tooltip, Tree, Spin } from "antd";
import { getClassBuiltTempAPI } from "src/api/dataClassification";
import { useSelector } from "src/hook";
import styles from "./index.module.scss";
import { useTreeStateManager } from './hooks/useTreeStateManager';

const { DirectoryTree } = Tree;

interface IBuiltInTemplateTreeProps {
  needRefreshTree?: boolean;
  clearCacheSignal?: number;
}

const BuiltInTemplateTree = React.memo(({ needRefreshTree, clearCacheSignal }: IBuiltInTemplateTreeProps): JSX.Element => {
  const { t } = useTranslation();
  const { selectedTemplateNodeInfo } = useSelector(state => state.dataClassification);
  const [treeHeight, setTreeHeight] = useState<number>(300);

  // 使用状态管理Hook
  const {
    treeData,
    expandedKeys,
    rootLoading,
    setExpandedKeys,
    onLoadData,
    refreshTree,
    clearTpDataCache,
    handleNodeSelect
  } = useTreeStateManager(needRefreshTree);


  useEffect(() => {
    queryTreeHeight();
    window.addEventListener('resize', queryTreeHeight);
    return () => {
      window.removeEventListener('resize', queryTreeHeight);
    }
  }, []);

  // 监听缓存清理信号
  useEffect(() => {
    if (clearCacheSignal && clearCacheSignal > 0) {
      clearTpDataCache(); // 清空所有缓存
    }
  }, [clearCacheSignal, clearTpDataCache]);

  const queryTreeHeight = () => {
    const clientHeight = document.documentElement.clientHeight
    const treeHeight = clientHeight > 520 ? clientHeight - 300 : 330
    setTreeHeight(treeHeight)
  }


  const onHandleDelete = (tp_id: any) => {
    if (!tp_id) return;
    Modal.confirm({
      centered: true,
      title: t('common.text.delete.tip'),
      onOk: async() => {
        // 调用删除API
        await getClassBuiltTempAPI({
          action: 'del',
          tp_id: String(tp_id)
        });

        message.success(t('common.message.delete_success'));
        refreshTree(); // 刷新树形结构
      },
    });
  }

  const titleRender = (node: any) => {

    const { isRoot = false } = node;
    return (
      <div className={styles.treeTitleItem}>
        <div className={styles.titleTxtWrap}>
          <span className={styles.titleTxt}>{node?.title}</span>
        </div>
        {
          isRoot &&
          <Tooltip title={t('common.btn.delete')}>
            <Button type="link" icon={<DeleteOutlined />} onClick={() => { onHandleDelete(node?.tp_id) }}></Button>
          </Tooltip>
        }
      </div>
    )
  }

  return (
    <Spin spinning={rootLoading} wrapperClassName={styles.spinContainer}>
      <DirectoryTree
        showIcon={false}
        height={treeHeight}
        className={styles.builtInTemplateTree}
        titleRender={titleRender}
        treeData={treeData}
        loadData={onLoadData}
        expandAction={false}
        selectedKeys={selectedTemplateNodeInfo?.key ? [selectedTemplateNodeInfo?.key] : undefined}
        onSelect={async (_key, { node }: any) => {
          await handleNodeSelect(_key, node);
        }}
        expandedKeys={[...expandedKeys]}
        onExpand={(expandedKeys) =>
          setExpandedKeys(expandedKeys)
        }
      ></DirectoryTree>
      {!rootLoading && !treeData?.length && (
        <div className={styles.treePlaceholder}>{t('db.auth.noElement')}</div>
      )}
    </Spin>
  );
});

export default BuiltInTemplateTree;

