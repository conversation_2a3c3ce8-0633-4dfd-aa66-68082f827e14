.builtInTemplateTree,.newTemplateTree {
  :global {
    .ant-tree-list {
      background-color: #f7f9fc;

      .ant-tree-treenode::before {
        bottom: 0;
      }
    }

    .ant-tree-treenode {
      width: 100%;
      padding: 0;
      border-bottom: 4px solid #f7f9fc;
      background-color: #fff;
      height: 36px;
      align-items: center;
      border-radius: 4px;
      overflow: hidden;

      &:last-child {
        height: 32px;
        border-bottom: 0;
      }
    }

    .ant-tree-node-content-wrapper {
      flex: 1;
    }

    .ant-tree-treenode-selected {
      &::before {
        background-color: #d6e3ff !important;
      }

      .ant-tree-node-content-wrapper.ant-tree-node-selected {
        color: rgba(0, 0, 0, 0.85) !important;
      }

      .ant-tree-switcher {
        color: rgba(0, 0, 0, 0.85) !important;
      }
    }
  }

  .treeTitleItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;

    .titleTxtWrap {
      display: flex;
      flex: 1;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;

      .titleTxt {
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .options {
      cursor: pointer;
      color: var(--primary-color);

      :global {
        .ant-dropdown-menu-item {
          color: var(--primary-color);
        }
      }
    }
  }
}
.treePlaceholder {
  text-align: center;
  margin-top: 24px;
  color: rgba(0, 0, 0, 0.75);
}