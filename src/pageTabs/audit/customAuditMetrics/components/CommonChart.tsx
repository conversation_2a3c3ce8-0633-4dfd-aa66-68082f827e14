import React, { useContext, useEffect, useMemo, useState } from "react"
import { getMoment<PERSON><PERSON><PERSON>, PreFilter } from "./PreFilter"
import { getChart } from "./ChartCard"
import { CUSTOM_AUDIT_CHART_ELEMENT, getChartApi } from "../constants"
import { chartSeriesListGeneratorForFY, dealChartDataHY, chartOptionsListGenerator, dealChartDataXY, mergeStandardAndAssistant, postFilterOpe, postFilterOpeForMatrix } from "../utils"
import { cloneDeep, isEmpty, uniq } from "lodash"
import styles from './components.module.scss'
import { useRequest, useSelector } from "src/hook"
import moment from "moment"
import { AuditOverviewContext } from "../../overview/AuditOverviewContext"
import AutoDisplayTable from "../../overview/components/AutoDisplayTable"
import { useTranslation } from "react-i18next"

export const CommonChart = (props: any) => {
  const { item } = props
  const { chartsCtrlList, onChartExportParamsChange } = useContext(AuditOverviewContext);
  const displayType = useMemo(()=>{
    return chartsCtrlList.find((i: any) => i.id === item.id)?.displayType || 'TABLE'
  }, [chartsCtrlList, item])
  const { t } = useTranslation()
  const { locales } = useSelector(state => state.login)
  const [chartData, setChartData] = useState<any>() // 图表的数据
  const [cData, setCData] = useState<any>() // 图表的数据
  const [chartNodes, setChartNodes] = useState<any>({})
  const [filterList, setFilterList] = useState<any[]>([
    { field: 'date', label: t("auays:filter_lbl.last_month"), value: [moment().subtract(30, 'd'), moment()] }
  ])
  const [filterNodes, setFilterNodes] = useState<any[]>([]); // 过滤条件
  const chartBaseInfo = CUSTOM_AUDIT_CHART_ELEMENT[item.chartKey]
  const { elements, dealType } = chartBaseInfo
  const [tableColumns, setTableColumns] = useState<any[]>([]) // 表格表头
  const [tableDataSource, setTableDataSource] = useState<any[]>([]) // 表格数据源

  // 获取图表数据
  const { run } = useRequest(getChartApi(item.chartKey), {
    manual: true,
    formatResult: (data: any) => {
      setCData(cloneDeep(data))
      const nodes: any[] = []
      if (dealType === 1) {
        filterNodes.map((node: any) => {
          if (!node.action) {
            const fieldData = uniq(data.map((item: any) => item[node.key] || '-'))
            nodes.push({ ...node, fieldData })
          }
          else {
            const fieldData = uniq(data.map((item: any) => item['amount'] || 0))
            nodes.push({ ...node, fieldData })
          }
        })
      }
      if (dealType === 2 || dealType === 3) {
        const { standard = [], assistant = [] } = data
        filterNodes.map((node: any) => {
          if (node?.type === 'title') {
            const fieldData = uniq(standard.map((item: any) => item[node.key] || '-'))
            nodes.push({ ...node, fieldData })
          }
          else if (node?.type === 'x') {
            const fieldData = uniq([...standard.map((item: any) => item[node.key] || '-'), ...assistant.map((item: any) => item[node.key] || '-')])
            nodes.push({ ...node, fieldData })
          }
          else if (node?.type === 'y' && node.action) {
            const fieldData = uniq(standard.map((item: any) => item['amount'] || 0))
            nodes.push({ ...node, fieldData })
          }
          else {
            const fieldData = uniq(assistant.map((item: any) => item['amount'] || 0))
            nodes.push({ ...node, fieldData })
          }
        })
      }
      if (dealType === 4) {
        filterNodes.map((node: any) => {
          let fieldData: any[] = []
          if (node?.type === 'x') {
            fieldData = uniq(Object.keys(data).map((key: any) => key || '-'))
          }
          else {
            let values: any[] = []
            for (const item of Object.values(data)) {
              if (item && typeof item === 'object') {
                for (const key of Object.keys(item)) {
                  values.push(key || '-');
                }
              }
            }
            fieldData = uniq(values)
          }
          nodes.push({ ...node, fieldData })
        })
      }
      setFilterNodes([...nodes])
    }
  })

  useEffect(() => {
    if (!isEmpty(filterNodes)) {
      const columns = filterNodes.map((item: any) => {
        const isNumerical: boolean = !!item.action
        return {
          title: item.title,
          dataIndex: isNumerical ? 'amount' : item.key,
          key: item.key,
          width: 200,
        }
      })
      setTableColumns(columns)
    }
  }, [filterNodes])

  // 事后过滤
  useEffect(() => {
    if (!isEmpty(cData)) {
      let data: any = cloneDeep(cData)
      filterNodes.map((node: any) => {
        if (dealType === 1) {
          const dataObj = postFilterOpe({ data }, node)
          data = dataObj?.data
          setCData(data)
        }
        else if (dealType === 2 || dealType === 3) {
          const { standard, assistant } = data
          let newData: any = {}
          if (node.type === 'x') newData = { standard, assistant }
          else if (node.type === 'y' && node.action) newData = { standard }
          else if (node.type === 'assistY' && node.action) newData = { assistant }
          else if (node.type === 'title') newData = { standard }
          const nData = postFilterOpe(newData, node)
          data = { ...data, ...nData }
          setCData(data)
        }
        else {
          data = postFilterOpeForMatrix(data, node)
          setCData(data)
        }
      })
    }
  }, [filterNodes])

  useEffect(() => {
    if (!isEmpty(item)) {
      const { cmdLogQuery = {}, chartKey, fieldInfos, tableName, metricsFilters = [] } = item
      const beforeFilterNodes: any = []
      const filterNodeList: any = []
      // 时间筛选
      if (cmdLogQuery?.executeBeginMs && cmdLogQuery?.executeEndMs) {
        const startTime = moment(cmdLogQuery?.executeBeginMs)
        const endtTime = moment(cmdLogQuery?.executeEndMs)
        beforeFilterNodes.push({
          field: 'date',
          label: getMomentAlias(startTime, endtTime),
          value: [startTime, endtTime]
        })
      }
      const filterFieldList = ['dbTypes', 'connectionName', 'depts', 'executors']
      for (const key in cmdLogQuery) {
        if (filterFieldList.includes(key)) {
          cmdLogQuery[key]?.map((v: string) => {
            beforeFilterNodes.push({
              field: key,
              label: v,
              value: v
            })
          })
        }
      }
      setFilterList([...beforeFilterNodes])
      if (chartKey !== 'TABLE') {
        fieldInfos.map((i: any) => {
          let source: string = ''
          let isNumerical: boolean = false
          elements.map((ele: any) => {
            if (ele.field === i.type) {
              source = t(ele.name);
              isNumerical = ele.isNumerical
            }
          })
          const newNode = {
            title: i.alias,
            baseTitle: i.alias,
            source,
            action: isNumerical ? (i.action || 'COUNT') : undefined,
            type: i.type,
            key: i.fieldName,
            info: { parentName: tableName },
          }
          const filter = metricsFilters.filter((item: any) => item.fieldName === newNode.key && item.fieldType === newNode.type)?.[0] || {}
          const hasSameNode = filterNodeList.filter((item: any) => item.key === newNode.key && item.title === newNode.title).length > 0
          filterNodeList.map((item: any) => {
            if (item.key === newNode.key && item.title === newNode.title) return { ...item, showSource: true }
            else return item
          })
          filterNodeList.push({ ...newNode, showSource: hasSameNode, isNumber: !!newNode.action, filter })
        })
        setFilterNodes([...filterNodeList])
      }
      else {
        fieldInfos.map((i: any) => {
          const newNode = {
            title: i.alias,
            baseTitle: i.alias,
            source: t("auays:custom_chart_axis.column"),
            action: i.action || undefined,
            isNumber: !!i.action,
            type: i.type,
            key: i.fieldName,
            info: { parentName: tableName },
          }
          const filter = metricsFilters.filter((item: any) => item.fieldName === newNode.key && item.fieldType === newNode.type)?.[0] || {}
          filterNodeList.push({ ...newNode, showSource: false, filter })
        })
        setFilterNodes([...filterNodeList])
      }
    }
  }, [item, locales])

  useEffect(() => {
    if (!isEmpty(item)) {
      const defaultEleNodes: any = {
        0: [],
        1: [],
        2: [],
        3: []
      }
      const cmdLogQuery: any = {}
      filterList.map((item: any) => {
        if (item.field === 'date') {
          if (item.value && Array.isArray(item.value)) {
            // 时间区间时分秒调整
            const startTime = moment(item.value[0].format('YYYY-MM-DD 00:00:00'))
            const endTime = moment(item.value[1].format('YYYY-MM-DD 23:59:59'))
            cmdLogQuery.executeBeginMs = startTime.valueOf();
            cmdLogQuery.executeEndMs = endTime.valueOf();
          }
        }
        else {
          if (!cmdLogQuery[item.field]) cmdLogQuery[item.field] = []
          cmdLogQuery[item.field]?.push(item.value)
        }
      })
      if (item.chartKey !== 'TABLE') {
        item?.fieldInfos.map((i: any) => {
          let eleIndex: number = 0
          let source: string = ''
          let isNumerical: boolean = false
          elements.map((ele: any, index: number) => {
            if (ele.field === i.type) {
              eleIndex = index;
              source = t(ele.name);
              isNumerical = ele.isNumerical
            }
          })
          const newNode = {
            title: i.alias,
            source,
            action: isNumerical ? (i.action || 'COUNT') : undefined,
            type: i.type,
            key: i.fieldName,
            info: { parentName: item.tableName },
          }
          defaultEleNodes[eleIndex].push(newNode)
        })
        setChartNodes({ ...defaultEleNodes })
      }
      else {
        item?.fieldInfos.map((i: any) => {
          const newNode = {
            title: i.alias,
            source: t("auays:custom_chart_axis.column"),
            action: i.action || undefined,
            isNumber: !!i.action,
            type: i.type,
            key: i.fieldName,
            info: { parentName: item.tableName },
          }
          defaultEleNodes[0].push(newNode)
        })
        setChartNodes({ ...defaultEleNodes })
      }
      onChartExportParamsChange(item.id, { ...item, cmdLogQuery })
      run({ ...item, cmdLogQuery })
    }
  }, [item, filterList, locales])

  useEffect(() => {
    // xy图例图表、饼图环形图、表
    if (dealType === 1 && cData?.length > 0) {
      if (item.chartKey === 'TABLE') {
        const fieldName = chartNodes[0].filter((node: any) => node.action)?.[0]?.key
        const newData = cData.map((item: any) => ({
          ...item,
          [fieldName]: item.amount
        }))
        const columns = chartNodes[0].map((item: any) => {
          return { title: item.title, dataIndex: item.key, key: item.key, isNumber: item.isNumber, ellipsis: true, width: 180 }
        })
        setChartData({ columns, data: newData })
      }
      else {
        setTableDataSource(cData)
        const baseData = dealChartDataXY(elements, chartNodes, cData)
        setChartData({ ...baseData })
      }
    }
    // 折线图和分区图
    else if (dealType === 2 && !isEmpty(cData)) {
      const hasFy = chartNodes[2].length > 0
      if (!hasFy) {
        setTableDataSource([...cData.standard])
        const baseData = dealChartDataXY(elements, chartNodes, cData.standard)
        setChartData({ ...baseData })
        setChartData({ ...baseData, hasFy })
      }
      else {
        const { standard = [], assistant = [] } = cData
        const newData = mergeStandardAndAssistant(standard, assistant)
        setTableDataSource([...newData])
        let xField: string[] = []
        let xName: string = ''
        let yName: string = ''
        let yField: string = 'amount'
        let fyName: string = ''
        let fyField: string = 'amountF'
        let tName: string = t("auays:axis_name.value_axis")
        elements.map((ele: any, index: number) => {
          if (chartNodes[index] && chartNodes[index].length > 0) {
            if (!ele.isNumerical && ele.required) {
              xField = chartNodes[index]?.map((node: any) => node.key)
              xName = chartNodes[index]?.map((node: any) => node.title).join('-')
            }
            else if (ele.isNumerical && ele.required) {
              yName = chartNodes[index]?.map((node: any) => node.title).join('-')
            }
            else if (ele.isNumerical && !ele.required) {
              fyName = chartNodes[index]?.map((node: any) => node.title).join('-')
            }
          }
        })
        // 如果辅助Y轴指标和Y轴指标相同就修改下series的name
        const legendFyName = fyName === yName ? (t("auays:custom_chart_axis.auxiliary_y_axis") + '-' + fyName) : fyName
        const { noNumberAxis } = chartOptionsListGenerator(newData, xField, 'amount')
        const { series, tooltips } = chartSeriesListGeneratorForFY(newData, xField, yField, fyField, [yName, legendFyName])
        setChartData({ noNumber: noNumberAxis, series, tooltips, xName, yName: [yName, fyName], hasFy, tName })
      }
    }
    // 折线和堆积柱状图和折线和簇状柱形图
    else if (dealType === 3 && !isEmpty(cData)) {
      const hasLine = chartNodes[2].length > 0
      if (!hasLine) {
        setTableDataSource([...cData.standard])
        const baseData = dealChartDataXY(elements, chartNodes, cData.standard)
        setChartData({ ...baseData })
        setChartData({ ...baseData, hasLine })
      }
      else {
        const { standard = [], assistant = [] } = cData
        const newData = mergeStandardAndAssistant(standard, assistant)
        setTableDataSource([...newData])
        const { noNumber, series, tooltips, xName, yName, tName } = dealChartDataXY(elements, chartNodes, cData.standard)
        const { seriesH, hyName } = dealChartDataHY(elements, chartNodes, cData.assistant)
        // 如果行Y轴指标和Y轴指标相同就修改下series的name
        const legendHyName = series?.filter((s: any) => s.name === hyName).length > 0 ? (t("auays:custom_chart_axis.row_y_axis") + '-' + hyName): hyName
        const newSeriesH = seriesH.map((s: any) => ({ ...s, name: legendHyName }))
        setChartData({ noNumber, series: [...series, ...newSeriesH], tooltips, xName, yName: [yName, hyName], tName, hasLine })
      }
    }
    // 矩阵
    else if (dealType === 4 && !isEmpty(cData)) {
      let values: any[] = []
      Object.values(cData).map((item: any) => {
        Object.keys(item)?.map((v: any) => values.push(v))
      })
      values = uniq(values)
      const valuesColumn = values.map((item: any) => ({ key: item, title: item, dataIndex: item, isNumber: chartNodes[1]?.[0]?.isNumber, ellipsis: true, width: 140 }))
      const topKey = chartNodes[0]?.map((item: any) => item.key)?.[0]
      const topColumn = chartNodes[0]?.map((item: any) => ({ key: item.key, title: item.title, dataIndex: item.key, ellipsis: true, width: 180 }))
      const totalColumn = [{ key: 'total', dataIndex: 'total', title: t("auays:tb_title.total"), isNumber: true, ellipsis: true, width: 80, fixed: 'right' }]
      const columns = [...topColumn, ...valuesColumn, ...totalColumn]
      const matrixData: any[] = []
      Object.keys(cData).map((key: any) => {
        const value = cData[key]
        const total = Object.values(value).reduce((a: any, b: any) => a + Number(b), 0)
        const dataItem = {
          [topKey]: key,
          ...value,
          'total': total,
        }
        matrixData.push(dataItem)
      })
      setChartData({ columns, data: matrixData })
    }
    else setChartData({})
  }, [cData, item, chartNodes, locales])

  return <div className={styles.commonChart} key={item.id}>
    {/* 事前筛选 */}
    <PreFilter defaultValue={filterList} callBack={setFilterList} id={item.id} />
    {
      (displayType === 'CHART' || ['TABLE', 'MATRIX'].includes(item.chartKey)) ?
        getChart(item.chartKey, chartData, false, item.id, '450px') :
        <AutoDisplayTable
          className={styles.displayTable}
          columns={tableColumns}
          dataSource={tableDataSource}
          pagination={false}
          scroll={{ x: 'fit-content', y: 'calc(45vh - 340px)' }}
        />
    }
  </div>
}