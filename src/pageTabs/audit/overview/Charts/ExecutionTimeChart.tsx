import DataSet from '@antv/data-set'
import { Chart } from '@antv/g2'
import { Radio, Spin, DatePicker, Select } from 'antd'
import dayjs, { Dayjs } from 'dayjs'
import * as _ from 'lodash'
import React, { useEffect, useState, useMemo, useContext } from 'react'
import {
  getAuditExecuteCostAvgUnit,
  getAuditConnDBNames,
  AuditSqlParams
} from 'src/api'
import { useRequest, useSelector, useDispatch } from 'src/hook'
import chartStyles from './chart.module.scss'
import { EmptyChart } from './EmptyChart'
import {
  getUnitBase,
  getCustomUnitBase,
  TIME_UNIT_EUM,
  formatColumns,
  formatDataSource,
  getFormattedTimeParams
} from './util'
import AutoDisplayTable from '../components/AutoDisplayTable'
import { AuditOverviewContext } from '../AuditOverviewContext'
import { useTranslation } from 'react-i18next'
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

interface OptionType {
  value: string | number;
  label: React.ReactNode;
}

export const ExecutionTimeChart = () => {
  const dispatch = useDispatch()
  const { chartsCtrlList, onChartExportParamsChange } = useContext(AuditOverviewContext);
  const displayType = useMemo(()=>{
    return chartsCtrlList.find((item: any) => item.id === -9)?.displayType || 'TABLE'
  }, [chartsCtrlList])

  const { t } = useTranslation();
  const { locales } = useSelector(state => state.login)
  const [timeRange, setTimeRange] = useState<1 | 7 | 30 | any>(7)
  const [rangePickerTimes, setRangePickerTimes] = useState<string[] | null>(
    [dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
    dayjs().endOf('d').format('YYYY-MM-DD')]
  )
  const [selectedConnectionName, setSelectedConnectionName] = useState<string | undefined>(undefined)
  const [dbName, setDbName] = useState<string[]>([])
  const [tableColumns, setTableColumns] = useState<any[]>([]) // 表格表头
  const [tableDataSource, setTableDataSource] = useState<any[]>([]) // 表格数据源

  // connection name options
  const { data:  connectionNameOptions } = useRequest(() => {
    const params = getFormattedTimeParams(timeRange, rangePickerTimes);
    return getAuditExecuteCostAvgUnit(params)
  }, {
    refreshDeps: [timeRange, rangePickerTimes],
    formatResult: (data) => {
      if (!data) return []
      const connectionNames = [...new Set(data?.map(item => item?.connectionName))];
      return connectionNames.map(name => ({
        value: name,
        label: name
      }))
    },
  })

  // db name options
  const { data: dbNames } = useRequest(
    () => {
      if (selectedConnectionName) {
        return getAuditConnDBNames(selectedConnectionName)
      }
      else {
        return Promise.resolve()
      }
    },
    {
      formatResult: (data) => {
        return (
          data &&
          data.map((db: string) => ({
            label: db,
            value: db,
          }))
        )
      },
      refreshDeps: [selectedConnectionName],
    },
  )
 
  const { data = [], loading } = useRequest(
    () => {

      const timeParams = getFormattedTimeParams(timeRange, rangePickerTimes);
      let defaultParams: AuditSqlParams = {
        ...timeParams,
        dbName
      }
      if (selectedConnectionName) {
        defaultParams.connectionName = selectedConnectionName;
      }

      onChartExportParamsChange(-9, {
        ...defaultParams,
        alias: t(TIME_UNIT_EUM[timeRange])
      })
      return getAuditExecuteCostAvgUnit(defaultParams)
    },
    {
      formatResult: (data) => {
        let baseUnit = getUnitBase(timeRange)

        if (rangePickerTimes) {
          baseUnit = getCustomUnitBase(
            rangePickerTimes[0],
            rangePickerTimes[1],
            timeRange === 1 ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD 00:00:00'
          )
        }

        return (
          new DataSet.DataView()
            .source(data.concat(baseUnit))
            // 填充刻度，例如值为 2021-02-8 00:00:00，unit 为 DAY，需要填充之前 timeRange 天的 unit string
            .transform({
              // 补全行
              type: 'fill-rows',
              groupBy: ['connectionName'],
              orderBy: ['unit'],
              fillBy: 'order',
            })
            .transform({
              // 补全列
              type: 'impute',
              field: 'executeCost', // 待补全字段
              groupBy: ['connectionName'], // 分组字段集（传空则不分组）
              method: 'value', // 补全为常量
              value: 0,
            })
            .rows.filter(({ connectionName }) => connectionName)
            .map(({ unit = '', ...rest }) => ({
              unit: timeRange === 1 ? new Date(unit).getHours() : unit,
              ...rest,
            }))
        )
      },
      refreshDeps: [
        timeRange,
        rangePickerTimes,
        dbName,
        selectedConnectionName,
      ],
    },
  )

  // 渲染语句明细
  const gotoStatementDetail = (params: any) => {
    dispatch(setOverviewPageState('statement_detail'))
    dispatch(setOverviewPageDetailParams(params))
  }

  const renderExecutionTimeChart = (container: string, data: any[]) => {
    const chart = new Chart({
      container,
      autoFit: true,
    })
    // data = data.sort((a, b) => b.executeCost - a.executeCost)
    data = data.sort((a, b) => (dayjs(a.unit).isBefore(b.unit) ? -1 : 1))
    // console.log(data)
    chart.data(data)
    chart.scale({
      unit: {
        range: [0.02, 0.92],
        formatter: (v) => (v < 25 ? v + ' ' + t("auays:time_unit.hour") : dayjs(v).format('YYYY-MM-DD')),
      },
      executeCost: {
        formatter: (v) => (v || '0') + 'ms',
        nice: true,
      },
    })

    chart.tooltip({
      showCrosshairs: true,
      shared: true,
    })

    chart.line().position('unit*executeCost').color('connectionName')

    chart
      .point()
      .position('unit*executeCost')
      .color('connectionName')
      .shape('circle')
      .style({
        stroke: '#fff',
        lineWidth: 1,
      })
    chart.on('plot:click', (ev: any) => {
      const shape: string = ev?.data?.shape ?? ''
      if (ev?.data?.data) {
        let time: number[] = []
        let elseParams: any = {}
        switch (shape) {
          case 'circle':
            time = [Number(dayjs(ev?.data?.data?.unit).format('x')), Number(dayjs(ev?.data?.data?.unit).endOf('day').format('x'))];
            elseParams.connectionName = [ev?.data?.data?.connectionName]
            elseParams.databaseName = [ev?.data?.data?.dbName]
            break;
          case 'line':
            if (!rangePickerTimes) {
              time = [Number(dayjs().subtract(6, 'day').format('x')), Number(dayjs().format('x'))]
            }
            else {
              const startTimes = Number(dayjs(rangePickerTimes[0]).startOf('d').format('x'));
              const endTimes = Number(dayjs(rangePickerTimes[1]).endOf('d').format('x'));
              time = [startTimes, endTimes];
            }
            break;
          default:
            time = [Number(dayjs().subtract(6, 'day').format('x')), Number(dayjs().format('x'))]
        }
        const params = {
          timeRange: time,
          ...elseParams,
        }
        gotoStatementDetail(params)
      }
    })
    chart.render()
    return chart
  }

  useEffect(() => {
    if (!data || data.length <= 0) return
    // 图表
    if (displayType === 'CHART') {
      const chart = renderExecutionTimeChart('execution-time', data)
      return () => chart.destroy()
    }
    // 表格
    else {
      const sortRule = (a: any, b: any): number => {
        const rule = timeRange === 1 ? Number(a.unit) < Number(b.unit) : a.unit < b.unit
        return rule ? -1 : 1
      }
      let newData: any[] = data.sort(sortRule)
        .map((item: any) => {
          const unit = timeRange === 1 ? (item?.unit + t("auays:time_unit.hour")) : dayjs(item?.unit).format('YYYY-MM-DD')
          return { ...item, unit, amount: item.executeCost }
        })
      const columns = formatColumns(newData, t(TIME_UNIT_EUM[timeRange]), 'connectionName')
      const dataSource = formatDataSource(newData, 'connectionName', 'ms')
      setTableColumns(columns)
      setTableDataSource(dataSource)
    }
  }, [data, displayType, locales])

  const rangeValue = useMemo(() => {
    if (rangePickerTimes === null) {
      return null
    } else {
      const range = rangePickerTimes.map((timestamp) => dayjs(timestamp)) as [
        Dayjs,
        Dayjs,
      ]
      return range
    }
  }, [rangePickerTimes])

  return (
    <Spin spinning={loading}>
      <div className={chartStyles.executionTimeWrapper}>
        <div className={chartStyles.toolbar}>
          <Radio.Group
            buttonStyle="solid"
            size="small"
            value={timeRange}
            onChange={(e) => {
              setTimeRange(e.target.value);
              setSelectedConnectionName(undefined);
              setDbName([]);
              if (e.target.value === 1) {
                setRangePickerTimes([
                  dayjs().format('YYYY-MM-DD'),
                  dayjs().format('YYYY-MM-DD'),
                ])
              } else if (e.target.value === 7) {
                setRangePickerTimes([
                  dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
                  dayjs().format('YYYY-MM-DD'),
                ])
              } else {
                setRangePickerTimes([
                  dayjs().subtract(29, 'day').format('YYYY-MM-DD'),
                  dayjs().format('YYYY-MM-DD'),
                ])
              }
            }}
          >
            <Radio.Button value={1}>{t("auays:filter_time.today")}</Radio.Button>
            <Radio.Button value={7}>{t("auays:filter_time.seven_days")}</Radio.Button>
            <Radio.Button value={30}>{t("auays:filter_time.thirty_days")}</Radio.Button>
          </Radio.Group>
          <div>
            <DatePicker.RangePicker
              style={{ marginLeft: 10 }}
              //@ts-ignore
              value={rangeValue}
              onChange={(dates, dateStrings) => {
                setSelectedConnectionName(undefined);
                setDbName([]);
                if (!dateStrings[0]) {
                  setRangePickerTimes(null)
                  setTimeRange(7)
                } else {
                  const diffDays = dayjs(dateStrings[1]).diff(
                    dateStrings[0],
                    'day',
                  )
                  if(dayjs().isSame(dayjs(dateStrings[1]),'day')) {
                    switch(diffDays) {
                      case 0: setTimeRange(1); break;
                      case 7: setTimeRange(7); break;
                      case 30: setTimeRange(30); break;
                      default: setTimeRange(0); break;
                    }
                  }
                  setRangePickerTimes(dateStrings)
                }
              }}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            />
            <Select
              allowClear
              showSearch
              maxTagCount={1}
              value={selectedConnectionName}
              placeholder={t("auays:sele_ph.conn_name")}
              options={connectionNameOptions as OptionType[]}
              style={{ margin: '0 10px', width: 100 }}
              onChange={(_, option: any) => {
                setSelectedConnectionName(option?.value);
                setDbName([])
              }}
              dropdownStyle={{ maxWidth: 200 }}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            />
            <Select
              showSearch
              mode="multiple"
              maxTagCount={1}
              placeholder={t("auays:sele_ph.database_name")}
              value={dbName}
              options={dbNames}
              className={chartStyles.mulSelect}
              style={{ width: 150 }}
              dropdownStyle={{ maxWidth: 200 }}
              onChange={(value: string[]) => setDbName(value)}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            />
          </div>
        </div>
        <div id="execution-time" className={chartStyles.executionTime}>
          {
            _.isEmpty(data) || loading ? <EmptyChart></EmptyChart> :
              displayType === 'CHART' ? <></> :
                <AutoDisplayTable
                  columns={tableColumns}
                  dataSource={tableDataSource}
                  pagination={false}
                  scroll={{ x: 'fit-content', y: 'calc(55vh - 128px)' }}
                />
          }
        </div>
      </div>
    </Spin>
  )
}
