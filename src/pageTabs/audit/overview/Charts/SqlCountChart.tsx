import React, { useEffect, useState, useMemo, useContext } from 'react'
import { Chart } from '@antv/g2'
import DataSet from '@antv/data-set'
import dayjs, { Dayjs } from 'dayjs'
import { Radio, Spin, DatePicker, Select } from 'antd'
import { useRequest, useSelector, useDispatch } from 'src/hook'
import { getAuditSqlLogUnit, getSupportedConnectionTypes } from 'src/api'
import {
  ChartColors,
  ChartColorsGradient,
  getUnit,
  getUnitBase,
  getUnixTimeRange,
  getCustomUnitBase,
  formatColumns,
  formatDataSource,
  TIME_UNIT_EUM,
} from './util'
import { EmptyChart } from './EmptyChart'
import chartStyles from './chart.module.scss'
import AutoDisplayTable from '../components/AutoDisplayTable'
import _ from 'lodash'
import { AuditOverviewContext } from '../AuditOverviewContext'
import { useTranslation } from 'react-i18next'
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

export const SqlCountChart = () => {
  const dispatch = useDispatch()
  const { chartsCtrlList, onChartExportParamsChange } = useContext(AuditOverviewContext);
  const displayType = useMemo(() => {
    return chartsCtrlList.find((item: any) => item.id === -3)?.displayType || 'TABLE'
  }, [chartsCtrlList])

  const { t } = useTranslation()
  const { locales } = useSelector(state => state.login)
  const [dbTypes, setDBTypes] = useState<string[]>([])
  const [timeRange, setTimeRange] = useState<1 | 7 | 30 | any>(7)
  const [rangePickerTimes, setRangePickerTimes] = useState<string[] | null>(
    [dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
    dayjs().endOf('d').format('YYYY-MM-DD')]
  )
  const [tableColumns, setTableColumns] = useState<any[]>([]) // 表格表头
  const [tableDataSource, setTableDataSource] = useState<any[]>([]) // 表格数据源

  const { data = [], loading } = useRequest(
    () => {
      let defaultParams = {
        dbTypes,
        unit: getUnit(timeRange),
        ...getUnixTimeRange(timeRange),
      }
      if (rangePickerTimes) {
        const startTimes = Number(dayjs(rangePickerTimes[0]).startOf('d').format('x'));
        const endTimes = Number(dayjs(rangePickerTimes[1]).endOf('d').format('x'));

        defaultParams = {
          ...defaultParams,
          beginTimeMs: startTimes,
          endTimeMs: endTimes,
        }
      }
      onChartExportParamsChange(-3, {
        ...defaultParams,
        alias: t(TIME_UNIT_EUM[timeRange])
      })
      return getAuditSqlLogUnit(defaultParams)
    },

    {
      formatResult: (data) => {

        let baseUnit = getUnitBase(timeRange)

        if (rangePickerTimes) {
          baseUnit = getCustomUnitBase(
            rangePickerTimes[0],
            rangePickerTimes[1],
            //  7天和30天模式下，要规范时分秒的format
            timeRange === 1 ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD 00:00:00'
          )
        }
        return (
          new DataSet.DataView()
            .source(data.concat(baseUnit))
            // 填充刻度，例如值为 2021-02-08 00:00:00，unit 为 DAY，需要填充之前 timeRange 天的 unit string
            .transform({
              // 补全行
              type: 'fill-rows',
              groupBy: ['dbType'],
              orderBy: ['unit'],
              fillBy: 'order',
            })
            .transform({
              // 补全列
              type: 'impute',
              field: 'amount', // 待补全字段
              groupBy: ['dbType'], // 分组字段集（传空则不分组）
              method: 'value', // 补全为常量
              value: 0,
            })
            .rows.filter(({ dbType }) => dbType)
            .map(({ unit = '', ...rest }) => ({
              unit: timeRange === 1 ? new Date(unit).getHours() : unit,
              ...rest,
            }))
        )
      },
      refreshDeps: [timeRange, rangePickerTimes, dbTypes],
    },
  )

  const { data: connectionTypes = [] } = useRequest(
    getSupportedConnectionTypes,
    {
      formatResult: (data) => {
        return data?.map((dataSource) => ({
          label: dataSource?.dataSourceName,
          value: dataSource?.dataSourceName,
        }))
      },
    },
  )

  useEffect(() => {
    if (!data || data.length <= 0) return
    // 图表
    if (displayType === 'CHART') {
      const chart = renderSqlCountChart('sql-count', data)
      return () => chart.destroy()
    }
    // 表格
    else {
      const sortRule = (a: any, b: any): number => {
        const rule = timeRange === 1 ? Number(a.unit) < Number(b.unit) : a.unit < b.unit
        return rule ? -1 : 1
      }
      let newData: any[] = data.sort(sortRule)
        .map((item: any) => {
          const unit = timeRange === 1 ? (item?.unit + t("auays:time_unit.hour")) : dayjs(item?.unit).format('YYYY-MM-DD')
          return { ...item, unit }
        })
      const columns = formatColumns(newData, t(TIME_UNIT_EUM[timeRange]), 'dbType')
      const dataSource = formatDataSource(newData, 'dbType', t("auays:unit.times"))
      setTableColumns(columns)
      setTableDataSource(dataSource)
    }
  }, [data, displayType, locales])

  const renderSqlCountChart = (container: string, data: any[]) => {
    const chart = new Chart({
      container,
      autoFit: true,
    })

    const dbTypeWeightByTotal = data
      .reduce((prev, curr) => {
        const record = prev.find((record: any) => record.dbType === curr.dbType)
        if (!record) return prev.concat({ ...curr })
        record.amount += curr.amount
        return prev
      }, [])
      .sort((a: any, b: any) => b.amount - a.amount)
      .map((record: any) => record.dbType)
      .reverse()
    data.sort((a, b) => {
      // 双条件排序：日期升序，日期一样，总数更大的数据库类型靠前
      if (a.unit === b.unit) {
        return (
          dbTypeWeightByTotal.indexOf(b.dbType) -
          dbTypeWeightByTotal.indexOf(a.dbType)
        )
      }
      return a.unit < b.unit ? -1 : 1
    })

    chart.data(data)

    chart.scale({
      unit: {
        range: [0.02, 0.92],
        formatter: (v) => (v < 25 ? v + ' ' + t("auays:time_unit.hour") : dayjs(v).format('YYYY-MM-DD')),
      },
      amount: {
        minTickInterval: 1,
        range: [0, 0.95],
        formatter: (v) => (v || '0') + ' ' + t("auays:unit.times"),
      },
    })

    chart.tooltip({
      showCrosshairs: true,
      shared: true,
    })

    chart.axis('amount', {
      label: {
        formatter: (val) => {
          return val
        },
      },
    })

    chart.line().position('unit*amount').color('dbType', ChartColorsGradient)
    chart.area().position('unit*amount').color('dbType', ChartColorsGradient)

    // tooltip 会根据颜色去重，如果 line 和 area 颜色不一致，tooltip 标签会重复

    // 渲染语句明细
    const gotoStatementDetail = (params: any) => {
      dispatch(setOverviewPageState('statement_detail'))
      dispatch(setOverviewPageDetailParams(params))
    }

    chart
      .point()
      .position('unit*amount')
      .color('dbType', ChartColors)
      .shape('circle')
      .style({
        stroke: '#fff',
        lineWidth: 1,
      })
    // new Add -- 听绘图区plot的点击事件
    chart.on('plot:click', (ev: any) => {
      const shape: string = ev?.data?.shape ?? ''
      if (ev?.data?.data) {
        let time: number[] = []
        let databaseTypes: string[] = [...dbTypes]
        switch (shape) {
          case 'circle':
            time = [Number(dayjs(ev?.data?.data?.unit).format('x')), Number(dayjs(ev?.data?.data?.unit).endOf('day').format('x'))];
            databaseTypes = [ev?.data?.data?.dbType]
            break;
          case 'area':
            if (!rangePickerTimes) {
              time = [Number(dayjs().subtract(6, 'day').format('x')), Number(dayjs().format('x'))]
            }
            else {
              const startTimes = Number(dayjs(rangePickerTimes[0]).startOf('d').format('x'));
              const endTimes = Number(dayjs(rangePickerTimes[1]).endOf('d').format('x'));
              time = [startTimes, endTimes];
            }
            break;
          default:
            time = [Number(dayjs().subtract(6, 'day').format('x')), Number(dayjs().format('x'))]
        }
        const params = {
          timeRange: time,
          dbTypes: databaseTypes,
        }
        gotoStatementDetail(params)
      }
    })

    chart.render()
    return chart
  }

  const rangeValue = useMemo(() => {
    if (rangePickerTimes === null) {
      return null
    } else {
      const range = rangePickerTimes.map((timestamp) => dayjs(timestamp)) as [
        Dayjs,
        Dayjs,
      ]
      return range
    }
  }, [rangePickerTimes])

  return (
    <Spin spinning={loading}>
      <div className={chartStyles.sqlCountWrapper}>
        <div className={chartStyles.toolbar}>
          <Radio.Group
            buttonStyle="solid"
            size="small"
            value={timeRange}
            onChange={(e) => {
              setTimeRange(e.target.value);
              if (e.target.value === 1) {
                setRangePickerTimes([dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')])
              } else if (e.target.value === 7) {
                setRangePickerTimes([dayjs().subtract(6, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')])
              } else {
                setRangePickerTimes([dayjs().subtract(29, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')])
              }
            }}
          >
            <Radio.Button value={1}>{t("auays:filter_time.today")}</Radio.Button>
            <Radio.Button value={7}>{t("auays:filter_time.seven_days")}</Radio.Button>
            <Radio.Button value={30}>{t("auays:filter_time.thirty_days")}</Radio.Button>
          </Radio.Group>
          <div>
            <DatePicker.RangePicker
              style={{ marginLeft: 10 }}
              //@ts-ignore
              value={rangeValue}
              onChange={(dates, dateStrings) => {
                if (!dateStrings[0]) {
                  setRangePickerTimes(null)
                  setTimeRange(7)
                } else {
                  const diffDays = dayjs(dateStrings[1]).diff(
                    dateStrings[0],
                    'day',
                  )
                  if (dayjs().isSame(dayjs(dateStrings[1]), 'day')) {
                    switch (diffDays) {
                      case 0: setTimeRange(1); break;
                      case 7: setTimeRange(7); break;
                      case 30: setTimeRange(30); break;
                      default: setTimeRange(0); break;
                    }
                  }
                  setRangePickerTimes(dateStrings)
                }
              }}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            />
            <Select
              showSearch
              className={chartStyles.mulSelect}
              mode="multiple"
              showArrow
              maxTagCount={1}
              placeholder={t("auays:sele_ph.database_types")}
              options={connectionTypes}
              style={{ marginLeft: 10, width: 180 }}
              onChange={(values: string[]) => setDBTypes(values)}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            />
          </div>
        </div>
        <div id="sql-count" className={chartStyles.sqlCount}>
          {
            _.isEmpty(data) || loading ? <EmptyChart></EmptyChart> :
              displayType === 'CHART' ? <></> :
                <AutoDisplayTable
                  columns={tableColumns}
                  dataSource={tableDataSource}
                  pagination={false}
                  scroll={{ x: 'fit-content', y: 'calc(45vh - 128px)' }}
                />
          }
        </div>
      </div>
    </Spin>
  )
}
