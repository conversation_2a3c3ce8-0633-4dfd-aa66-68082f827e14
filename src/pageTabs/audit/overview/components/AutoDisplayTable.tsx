import { Table } from "antd"
import { TableProps } from "antd/lib/table"
import classNames from "classnames"
import React from "react"
import styles from './components.module.scss'

export interface IAutoDisplayTable extends TableProps<any> { }

const AutoDisplayTable = (props: IAutoDisplayTable) => {
  return (
    <Table
      scroll={{ x: 'fit-content', y: '100%' }}
      {...props}
      // className={classNames(styles.autoDisplayTable,props.className)}
    />
  )
}
export default AutoDisplayTable