{"code": 200, "message": "success", "data": {"error_info": "", "count": {"total": 251}, "datas": [{"id": 4958, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 0, "classify_name": "学校概括数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4959, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 0, "classify_name": "学生管理数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4960, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 0, "classify_name": "教学管理数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4961, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 0, "classify_name": "教职工管理数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4962, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 0, "classify_name": "科研管理数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4963, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 0, "classify_name": "财务管理数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4964, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 0, "classify_name": "资产与设备管理数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4965, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 0, "classify_name": "档案管理数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4966, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 0, "classify_name": "辅助数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4967, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4958, "classify_name": "学校基本数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4968, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4958, "classify_name": "学校委员会（领导小组）数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4969, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4958, "classify_name": "院系所单位数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4970, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4958, "classify_name": "班级数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4971, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4958, "classify_name": "学科点数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4972, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4958, "classify_name": "校区数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4973, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4958, "classify_name": "学科点统计数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4974, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4959, "classify_name": "学生基本数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4975, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4959, "classify_name": "本专科生新生数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4976, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4959, "classify_name": "毕业生相关数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4977, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4959, "classify_name": "体检、防疫数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4978, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4959, "classify_name": "学籍数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4979, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4959, "classify_name": "学位、学历数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4980, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4959, "classify_name": "经济资助数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4981, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4959, "classify_name": "社团（协会）辅助数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4982, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4959, "classify_name": "就业辅助数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4983, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4960, "classify_name": "教学管理数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4984, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4960, "classify_name": "教学计划数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4985, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4960, "classify_name": "排课数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4986, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4960, "classify_name": "选课数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4987, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4960, "classify_name": "教室管理数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4988, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4960, "classify_name": "教材数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4989, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4960, "classify_name": "教学成果数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4990, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4960, "classify_name": "评教数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4991, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4960, "classify_name": "考试安排数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4992, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4961, "classify_name": "教职工基本数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4993, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4961, "classify_name": "教职工考核数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4994, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4961, "classify_name": "离校数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4995, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4961, "classify_name": "专家管理辅助数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4996, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4961, "classify_name": "兼职数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4997, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4961, "classify_name": "学习进修数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4998, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4961, "classify_name": "住房数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 4999, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4962, "classify_name": "科技项目数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5000, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4962, "classify_name": "科研机构数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5001, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4962, "classify_name": "科技成果数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5002, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4962, "classify_name": "学术交流数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5003, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4963, "classify_name": "账务管理数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5004, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4963, "classify_name": "账务数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5005, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4963, "classify_name": "项目经费数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5006, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4963, "classify_name": "往来账数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5007, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4963, "classify_name": "教职工个人收入数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5008, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4963, "classify_name": "票据数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5009, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4964, "classify_name": "学校用地数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5010, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4964, "classify_name": "学校建筑物数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5011, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4964, "classify_name": "仪器设备管理数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5012, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4965, "classify_name": "文件实体", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5013, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4965, "classify_name": "机构人员实体", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5014, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4965, "classify_name": "业务实体", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5015, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4966, "classify_name": "个人数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5016, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4966, "classify_name": "通讯数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5017, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4966, "classify_name": "家庭情况数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5018, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4966, "classify_name": "校医数据", "level_id": 0, "label_ids": "", "description": "", "label_names": ""}, {"id": 5019, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4967, "classify_name": "学校基本数据", "level_id": 77, "label_ids": "30074&30072&29399&30073&30749&30076&30747&29654&31198&29469&31015&30079&30078&30789&30748&30085&30086&30305&29831&31135&30788", "description": "", "label_names": "学校代码,学校主管部门码,985工程院校状况,学校举办者码,校长工号,学校办学类型码,校庆日,党委负责人号,行政区划码,主页地址,组织机构码,学校地址,学校名称,法定代表人号,校长姓名,学校英文名称,学校邮政编码,建校年月,历史沿革,联系电话,法人证书号"}, {"id": 5020, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4968, "classify_name": "委员会（领导小组）数据", "level_id": 78, "label_ids": "30025&30640&30027&30384&30029&30526&30701&30026&30028&29551&30700", "description": "", "label_names": "委员人员号,是否常设,委员会简称,成立日期,委员会职责,撤销日期,本届换届年月,委员会名称,委员会编号,任职码,本届到期年月"}, {"id": 5021, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4969, "classify_name": "院系所单位数据", "level_id": 77, "label_ids": "29805&29842&29683&30235&29898&30236&30513&29889&31479&30596&29429&29413&30163&29807&30575&30591&29814&30718&29795&29798&30843&30883&29998&29806&30956&29804&29656&29459&29815&31481&30628&29568&30722&29843&30740", "description": "", "label_names": "单位简拼,原部门ID,公司数,工勤编制数,合同工人数,工厂数,排序号,变更日期,院系所总编制数,教辅编制数,专业数,上级部门ID,实验室数,单位类别码,教学编制数,教研室数,单位英文名称,校企共建实训基地数,单位办别码,单位地址,生产编制数,直属编制数,失效日期,单位简称,科研编制数,单位有效标识,党政编制数,临时工人数,单位英文简称,隶属单位号,是否实体,企业编制数,校内实训基地数,原隶属单位号,校外实训基地数"}, {"id": 5022, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4970, "classify_name": "班级数据", "level_id": 78, "label_ids": "30834&30832&29921&30835&30729&29419&30558&31395&30557&31392&30831&30662&30830&30306&30428&30462&30038&31391&31011&31136", "description": "", "label_names": "班级名称,班级人数,团支书学号,班长学号,校区代码,专业代码,教务处单位名称,辅导员电话,教务处单位号,辅导员姓名,班号,是否订单班,班主任工号,建班年月,所属年级,报到日期,学习委员学号,辅导员号,纪律委员学号,联系领导工号"}, {"id": 5023, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4971, "classify_name": "学科点基本数据", "level_id": 78, "label_ids": "29933&29822&29403&29479&30101&30891&29404&29823&29478&30100&30896&30106", "description": "", "label_names": "国家重点学科状况,博士后站状况,一级学科授权年月,二级学科码,学科点简介,省部级重点学科状况,一级学科码,博士点授权年月,二级学科授权年月,学科授权码,硕士点授权年月,学科门类码"}, {"id": 5024, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4972, "classify_name": "校区数据", "level_id": 77, "label_ids": "30732&30733&30734&30730&30731&30737&30738", "description": "", "label_names": "校区名称,校区地址,校区联系电话,校区传真电话,校区号,校区负责人号,校区邮政编码"}, {"id": 5025, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4973, "classify_name": "学科点统计数据", "level_id": 78, "label_ids": "30890&29934&29932&29935&29931&30898&29826&29405&29824&29940&30888&29692&30889&30152&29538&29821&29537&29825&29426&29406&30151&29455&30892&29457&29454&29820&30240&30897&29926&29860&30699&30132&29928&29927", "description": "", "label_names": "省部级重点学科数（二级）,国家重点实验室数,国家重点学科数（二级）,国家重点（培育）学科数,国家重点学科数（一级）,硕导合计数,博硕导合计数,万元以上仪器设备总值,博士生合计数,图书资料投资额,省部级研究院数,其他投资额,省部级重点学科数（一级）,实际硕士点数,仪器设备投资额,博士后流动站数,仪器设备总值,博导合计数,专业实验室面积,万元以上仪器设备数,实际博士点数,中外文藏书册数,省部级重点实验室数,中科院院士数,中外文期刊种类,博士后合计数,工程院院士数,硕士生合计数,国家工程技术研究中心数,双聘院士数,本学科投资总额,定期公开出版专业刊物数,国家科研基地/中心数,国家工程研究中心数"}, {"id": 5026, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4974, "classify_name": "学生基本数据", "level_id": 79, "label_ids": "29596&30624&30342&30350&30833&30827&29770&30807&29734&30399&30345&30847&30024&30642&30108&31367&30849&30549&31372&31435&30697&31371&30786&31097&30061&31155&30348&30672&29421&30343&30997&30351&30353&30344&30846&31193&30340&30809&29958&29936&30338&29620", "description": "", "label_names": "体重,是否在校,录取成绩,录取联系地址,班级代码,现家庭地址,加入党团日期,港澳台侨外码,出生日期,户籍类别,录取收件人,生源地区代码,姓名拼音,是否报到,学籍状态,身份证件类,生源省份代码,政治面貌,身高,通知书号,本人成份,身份证户籍地址,民族,考生号,学号,英文姓名,录取科类代码,曾用名,专业号,录取批次,籍贯,录取联系电话,录取邮政编码,录取投档专业码,生源单位名称,血型码,录取序号,照片,培养类别码,国籍/地区码,录取专业,信仰宗教码"}, {"id": 5027, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4974, "classify_name": "学习简历数据", "level_id": 78, "label_ids": "30037&30036&31114&30424&30046&30045&30433&30043&30048", "description": "", "label_names": "学习单位,学习内容,职务名称,所学专业名称,学习证明人,学习终止日期,所获学位码,学习简历备注,学习起始日期"}, {"id": 5028, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4974, "classify_name": "住宿数据", "level_id": 78, "label_ids": "30914&30194&30614&30401&30277&30629&30193&30630&30615&29659&30913&31291&30181&30677&29993&30400&30175&29583&29994", "description": "", "label_names": "社区（村）名称,宿舍电话,是否住校,房东电话,床位号,是否审批,宿舍房间号,是否宿舍长,是否允许修改,入住日期,社区代码,详细居住地址,审批说明,最后审批日期,外宿入住日期,房东姓名,审批人工号,住宿年度,外宿迁出日期"}, {"id": 5029, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4974, "classify_name": "户口状况数据", "level_id": 78, "label_ids": "30395&30393&30666&30397&30392&30391&30390&29662", "description": "", "label_names": "户口是否在校,户口原省市码,是否需要迁入户口,户口迁出日期,户口原所在地址,户口交办日期,户口交办情况码,入学前户口类别码"}, {"id": 5030, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4974, "classify_name": "学生来源数据", "level_id": 78, "label_ids": "30203&30714&29666&30096&29660&29837&30131&29661&29836", "description": "", "label_names": "就读方式码,来源国别/地区码,入学方式码,学生来源码,入学前学位码,原学校名称,定向/委培单位,入学前学历码,原学号"}, {"id": 5031, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4975, "classify_name": "本专科生考生数据", "level_id": 79, "label_ids": "30777&30970&30906&30848&30298&31110&31080&31098&31099&29593&30191&30778&29705&29999", "description": "", "label_names": "毕业中学号,竞赛获奖,社会工作,生源地码,应试外国语种码,考试课程名称,考区码,考生既往病史,考生特长,体检结论,家族病史,毕业中学邮政编码,准考证号,奖励与惩处"}, {"id": 5032, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4975, "classify_name": "本专科生考生总数据", "level_id": 78, "label_ids": "31540&29665&29740&31474&31475&30951", "description": "", "label_names": "高考总分,入学总分,分数,附加分,附加分类别码,科目码"}, {"id": 5033, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4975, "classify_name": "本专科生录取数据", "level_id": 78, "label_ids": "30703&30347&30618&30354&30349&30341", "description": "", "label_names": "本校专业号,录取省市专业码,是否公费医疗,录取院系所号,录取类别码,录取志愿"}, {"id": 5034, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4976, "classify_name": "结束学业数据", "level_id": 79, "label_ids": "31212&31389&31035&31211&31034&31037&29742&31387&30781", "description": "", "label_names": "补行毕业日期,辅修结业证书号,结束学业原因说明,补行毕业原因,结业证书号,结束学业码,分流方向码,辅修学位证书号,毕业证书号"}, {"id": 5035, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4976, "classify_name": "毕业生求职数据", "level_id": 79, "label_ids": "29996&31234&30018&30378&29851&30907&30377&30822", "description": "", "label_names": "外语水平,计算机水平,奖惩情况,意向工作行业,参加科研情况,社会工作概况,意向工作地区,特长及爱好"}, {"id": 5036, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4976, "classify_name": "毕业生就业数据", "level_id": 78, "label_ids": "30853&29790&30779&30519&29791&29816&30200&30226&29803&30518&29788&30804&29808&29793&30905&30463&29800&30757", "description": "", "label_names": "用人单位用人形式码,协议年限,毕业去向码,接收地邮政编码,协议签订日期,单位行业码,就业落实方式码,工作岗位性质码,单位提供的待遇,接收单位,协议书编号,派遣费,单位经济性质码,单位主管部门,社会单位性质码,报到证号,单位所在地区划码,档案接收地址"}, {"id": 5037, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4976, "classify_name": "毕业生离校数据", "level_id": 78, "label_ids": "29908&30115&29920&29942&30089&30092&30117&30091&29917&29910&29909&30784&29911&31328&29918&30116&30780&31327&30090&31329&29941&30783&30112&29944&29919&31326&29943", "description": "", "label_names": "后勤中心不通过原因,学院审批,团委审批时间,图书馆审批,学生处不通过原因,学生处审批时间,学院审批时间,学生处审批姓名,团委不通过原因,后勤中心审批姓名,后勤中心审批,毕业证发放登记时间,后勤中心审批时间,财务处审批姓名,团委审批,学院审批姓名,毕业学年,财务处审批,学生处审批,财务处审批时间,图书馆不通过原因,毕业证发放登记,学院不通过原因,图书馆审批时间,团委审批姓名,财务处不通过原因,图书馆审批姓名"}, {"id": 5038, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4976, "classify_name": "离校手续数据", "level_id": 79, "label_ids": "30637&30632&30900&30636&30638&31012&29939&30651&30635&30398&30631&30110&30650", "description": "", "label_names": "是否已领毕业证,是否已处理网络帐号,确认落户地址,是否已领报到证,是否已领派遣费,组织关系办理情况,图书未还或欠费,是否欠医疗费,是否已退宿舍,户口迁出状况码,是否已办理校园卡,学费欠费金额,是否欠交学费"}, {"id": 5039, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4977, "classify_name": "体检数据", "level_id": 79, "label_ids": "29594&29591&29592&29595", "description": "", "label_names": "体检说明,体检日期,体检结果,体检项目类别码"}, {"id": 5040, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4977, "classify_name": "防疫注射数据", "level_id": 79, "label_ids": "30793&30695&30763&30794", "description": "", "label_names": "注射日期,未注射原因码,检查后结果,注射类别码"}, {"id": 5041, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4978, "classify_name": "学籍基本数据", "level_id": 78, "label_ids": "29431&31183&31419&31478&30095&30422&29664&30420&30097&29956&30197&30626", "description": "", "label_names": "专业码,获得学历方式码,连读方式码,院系所号,学生当前状态码,所在班号,入学年月,所在年级,学生类别码,培养方式码,导师号,是否学分制"}, {"id": 5042, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4978, "classify_name": "辅修专业、双学位数据", "level_id": 78, "label_ids": "31390&29477&29476&31388&31386", "description": "", "label_names": "辅修院系所号,二学位院系所号,二学位专业码,辅修学校码,辅修专业码"}, {"id": 5043, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4978, "classify_name": "缴纳学费数据", "level_id": 78, "label_ids": "31073&31072", "description": "", "label_names": "缴费状况码,缴费日期"}, {"id": 5044, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4978, "classify_name": "注册数据", "level_id": 79, "label_ids": "30792&30694&30791&30107&30693&30663", "description": "", "label_names": "注册状况码,未注册原因,注册日期,学籍,未注册/报到去向,是否请假"}, {"id": 5045, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4978, "classify_name": "成绩数据", "level_id": 78, "label_ids": "30263&30387&31094&30385&31048&31306&30976&29878&29633&30064&29741&31103&30386&31305&31102&31309&31300&29557&31182&30825&30817&30055&31061&31310", "description": "", "label_names": "平时成绩,成绩录入时间,考核方式,成绩录入人号,绩点,课程等级成绩码,等级类考试成绩,取得方式,修读性质,学期码,分数类考试成绩,考试性质码,成绩录入日期,课程成绩,考试形式码,课程类别一,课程名称,任课教师工号,获得学分,环节类别,特殊情况,学分,综合成绩,课程类别二"}, {"id": 5046, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4978, "classify_name": "惩处数据", "level_id": 79, "label_ids": "30374&30862&29985&29981&29980&29982&29983&31418&30179&31417&29984&30863&30369&30373&30371&31010&30375&30372&31416&30376&30180&30674&30866&29971&30370&30368", "description": "", "label_names": "惩处撤销日期,申委会审议日期,处分日期,处分名称码,处分原因,处分撤消文号,处分撤消日期,违纪类别码,审批日期时间,违纪简况,处分文号,申委会审议结论,惩处单位,惩处撤消文号,惩处名称,纪律处分码,惩处文号,惩处撤消原因,违纪日期,惩处日期,审批结果,最后修改人工号,申诉日期,填报人编号,惩处原因,惩处内容"}, {"id": 5047, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "学位、学历数据", "level_id": 78, "label_ids": "30503&30502&30041&30051&29423&31036&30425&30060&30785&30547&30655&30423&30057&29425&31181&31177&30059&29424&29622&31279&31178&30052&31179&30645&31278&30504&30782&30042&30054&30646&29751&30109&29427&30654", "description": "", "label_names": "授学位国家/地区码,授学位单位名称,学习形式码,学位委员会主席姓名,专业学位类别码,结束学业年月,所学专业码,学历证书号,毕肄业学校或单位,攻读类型码,是否第一学历获得,所学专业,学制,专业学科分类,获得学位码,获学位专业码,学历码,专业学位领域码,修改人,证明人电话,获学位日期,学位层次,获学位门类码,是否最高学位,证明人姓名,授学历国家地区码,毕业证印制号,学习方式码,学位证书号,是否最高学历,创建人,学缘,专业性质,是否第一学历"}, {"id": 5048, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "学位论文数据", "level_id": 79, "label_ids": "29456&31243&31256&31262&31240&31259&31245&31257&31254&31242&31156&31260&31252", "description": "", "label_names": "中文摘要,论文字数,论文英文题目,论文题目,论文中图分类号,论文起始日期,论文密级码,论文获奖级别码,论文英文主题词,论文主题词,英文摘要,论文选题来源码,论文终止日期"}, {"id": 5049, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "论文评审数据", "level_id": 78, "label_ids": "31289&31258&31244&31290&31247&31287&31288&31251", "description": "", "label_names": "评阅人是否博导,论文评审日期,论文学位要求评审结论,评阅人职务码,论文水平评审结论,评阅人,评阅人单位,论文级别评审结论"}, {"id": 5050, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "论文答辩委员数据", "level_id": 78, "label_ids": "30980&30982&30983&30979&30981", "description": "", "label_names": "答辩委员姓名,答辩委员是否博导,答辩委员职务码,答辩委员单位,答辩委员是否主席"}, {"id": 5051, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "授予学位评审数据", "level_id": 78, "label_ids": "30745&29736&30746&30978&30744&29738&30692&31248&29737", "description": "", "label_names": "校学位会表决日期,分委会名称,校学位会表决结果,答辩会毕业结果,校学位会结论码,分委会表决结果,未授/缓授原因,论文答辩日期,分委会表决日期"}, {"id": 5052, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "学生研究训练活动数据", "level_id": 78, "label_ids": "31514&31180&29877&31497&30964&31507&31237&30698&30199&31512", "description": "", "label_names": "项目起始日期,获得专利情况,发表论文情况,项目成绩,立项人号,项目终止日期,训练项目名称,本人角色,导师评语,项目获奖情况"}, {"id": 5053, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "课外赛事数据", "level_id": 78, "label_ids": "30972&30966&30967&30968&30500&30969&29853&30971", "description": "", "label_names": "竞赛项目名称,竞赛举办单位,竞赛地点,竞赛日期,指导老师号,竞赛级别,参赛形式,竞赛获奖情况"}, {"id": 5054, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "军训数据", "level_id": 77, "label_ids": "29700&29701&29702&29703&29704", "description": "", "label_names": "军训地点,军训成绩码,军训终止日期,军训起始日期,军训部队"}, {"id": 5055, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "预征入伍信息数据", "level_id": 79, "label_ids": "30595&29965&29684", "description": "", "label_names": "教育鉴定,基层武装部意见,公安鉴定"}, {"id": 5056, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "社会实践活动数据", "level_id": 78, "label_ids": "30147&30141&30144&30148&30145&30142&30149&31277&30146&30150", "description": "", "label_names": "实践类别码,实践单位,实践成果,实践终止日期,实践成绩码,实践地点,实践起始日期,证明人,实践等级码,实践项目"}, {"id": 5057, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "三助活动数据", "level_id": 77, "label_ids": "29409&29410&29407&29411&29408&29412", "description": "", "label_names": "三助成绩码,三助类别码,三助内容,三助终止日期,三助单位号,三助起始日期"}, {"id": 5058, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4979, "classify_name": "社会工作数据", "level_id": 77, "label_ids": "30909&30229&30232&30908", "description": "", "label_names": "社会工作部门,工作终止年月,工作起始年月,社会工作职务"}, {"id": 5059, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "奖学金数据", "level_id": 78, "label_ids": "30016&31168&30015&29868&30017&30014", "description": "", "label_names": "奖学金类型码,获奖年月,奖学金等级,发放日期,奖学金额,奖学金名称"}, {"id": 5060, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "助学金数据", "level_id": 78, "label_ids": "29774&31349&31185&29642&30022&29773&29641&31356&31352", "description": "", "label_names": "助学金额,资助单位/个人,获资助年月,停发年月,始发年月,助学金名称,停发原因,资金来源码,资助等级"}, {"id": 5061, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "临时困难补助数据", "level_id": 79, "label_ids": "31206&31205&31207", "description": "", "label_names": "补助日期,补助原因,补助金额"}, {"id": 5062, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "勤工助学数据", "level_id": 78, "label_ids": "30362&30684&29778&29777&30685&29771&30227&30851&30358", "description": "", "label_names": "总工作小时,月发放金额,勤工类别码,勤工助学岗位,月平均工作小时,助学单位类别码,工作性质,用人单位,总发放金额"}, {"id": 5063, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "伙食补贴数据", "level_id": 78, "label_ids": "31220&31216&31214", "description": "", "label_names": "补贴金额,补贴日期,补贴原因"}, {"id": 5064, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "绿色通道数据", "level_id": 78, "label_ids": "30660&30659&29493&29494", "description": "", "label_names": "是否缓交学费,是否缓交住宿费,享受日期,享受绿色通道原因"}, {"id": 5065, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "学费减免数据", "level_id": 78, "label_ids": "29708&29706&29707", "description": "", "label_names": "减免金额,减免原因,减免日期"}, {"id": 5066, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "校内无息贷款数据", "level_id": 79, "label_ids": "29644&29645&29635", "description": "", "label_names": "偿还借款日期,偿还借款金额,借款原因"}, {"id": 5067, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "助学贷款数据", "level_id": 79, "label_ids": "30302&31343&30473&31340&31406&30641&31341&31342&31414&30251&31413&31075&31344&31534&31412&30265&31346&31347&31345&29758&31407&30252&31153&31184&29930&31415&29646&29759", "description": "", "label_names": "延期年限,贷款总金额,担保方式,贷款合同号,还款方式,是否延期,贷款学费金额,贷款年数,违约本金,已偿还利息,违约时间,罚息,贷款生活费金额,首期还款日,违约利息,年利率,贷款类型,贷款银行,贷款申请年月,到期应偿还利息,还款终止日,已偿还本金,自付利息起始日,获贷日期,国家贴息起始日,违约金额,偿还情况统计日期,到期应偿还本金"}, {"id": 5068, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "助学贷款发放数据", "level_id": 79, "label_ids": "31337&29866&29864&30773&31000&30772&30771&31339&31338&29869", "description": "", "label_names": "贷款发放批次,发放学费,发放住宿费,款项说明,类型明细,款项税后金额,款项税前金额,贷款发放金额,贷款发放日期,发放类型码"}, {"id": 5069, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "其他资助数据", "level_id": 79, "label_ids": "31350&31353&31351", "description": "", "label_names": "资助名称,资助金额,资助日期"}, {"id": 5070, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "学费补偿与贷款代偿数据", "level_id": 79, "label_ids": "29757&30111&30224&29756&29772&30988&31204&30225&29657&31335&31336&31334", "description": "", "label_names": "利息计息起始日,学费补偿金额,工作单位名称,利息计息截止日,助学贷款类型,签约服务年限,补代偿方式,工作单位地址,入伍日期,贷款代偿本金,贷款代偿金额,贷款代偿利息"}, {"id": 5071, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "学费补偿与贷款代偿发放账号数据", "level_id": 79, "label_ids": "31439&30081&31440&30099&30093&30094&30080&31438&30098&30088&31437&30087", "description": "", "label_names": "邮局收款人电话,学校开户行地址,邮局收款人邮编,学生银行账号,学生开户行名称,学生开户行地址,学校开户行名称,邮局收款人姓名,学生银行户名,学校银行账号,邮局收款人地址,学校银行户名"}, {"id": 5072, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "学生应收明细", "level_id": 79, "label_ids": "31318&31317&30287&30539&30818", "description": "", "label_names": "调整金额,调整方式码,应收金额,收费学年,特殊说明"}, {"id": 5073, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4980, "classify_name": "学费补偿与贷款代偿发放数据", "level_id": 79, "label_ids": "29871&29867&29865", "description": "", "label_names": "发放金额,发放批次,发放利息金额"}, {"id": 5074, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4981, "classify_name": "社团（协会）辅助数据", "level_id": 78, "label_ids": "30921&31224&30182&30915&30916&30920&30918&30917&30498&30919&30499", "description": "", "label_names": "社团负责人号,解散日期,审核状态,社团名称,社团照片路径,社团网址,社团类型,社团简介,挂靠单位号,社团编号,指导教师号"}, {"id": 5075, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4982, "classify_name": "用人单位需求数据", "level_id": 78, "label_ids": "29812&29818&30679&31483&30854&31484&29813&31485&30852&29794&29802&30678&31129&31486&31130", "description": "", "label_names": "单位联系地址,单位邮政编码,最近联系年份,需求人数,用人单位编号,需求生源专业码,单位联系电话,需求生源学历码,用人单位名称,单位主页地址,单位描述,最近就业人数,联系人电子信箱,需求生源条件,联系人电话"}, {"id": 5076, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4982, "classify_name": "用人单位网上招聘数据", "level_id": 78, "label_ids": "30491&30389&30483&29862&29863&30490&30488&31466", "description": "", "label_names": "招聘类别,截止日期,招聘内容,发布日期,发布范围,招聘标题,招聘年份,链接网址"}, {"id": 5077, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4982, "classify_name": "招聘会数据", "level_id": 77, "label_ids": "29912&30492&30480&30475&29799&30205&31125&30486&31132&29496&30489&29497&30493&30487&30476&30485&31131&30481&30294&30482&30478&30479&31133&30484&29849&30477&30474", "description": "", "label_names": "后续安排,招聘范围,招聘会结束日期,招聘会举办日期,单位意见,岗位待遇,联系人,招聘备注,联系地址,人事处审核日期,招聘截止日期,人事处意见,招聘要求,招聘岗位,招聘会公布日期,招聘原因,联系信箱,招聘会编号,应聘程序,招聘信息发布日期,招聘会标题,招聘会类别码,联系方式,招聘单位号,参加招聘单位,招聘会年份,招聘会举办地点"}, {"id": 5078, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4983, "classify_name": "专业信息数据", "level_id": 77, "label_ids": "30050&30555&30049&31361&29955&30895&29432&30556&30105&29954&29430&30696&30307&29422&29433&30106", "description": "", "label_names": "学位名称,教务单位号,学位代码,起始学期,培养层次码,研究生专业码,专业简称,教务单位名称,学科门类名称,培养层次,专业方向号,本专科专业码,建立年月,专业名称,专业英文名称,学科门类码"}, {"id": 5079, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4983, "classify_name": "课程数据", "level_id": 77, "label_ids": "30589&31299&30360&31308&29914&31311&31307&31303&31313&30157&30143&30836&29852", "description": "", "label_names": "教材,课程号,总学时,课程类别,周学时,课程类型,课程简介,课程性质,课程负责人号,实验学时,实践学时,理论学时,参考书目"}, {"id": 5080, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4984, "classify_name": "总体计划数据", "level_id": 77, "label_ids": "30361&30359&30284&29957&31229", "description": "", "label_names": "总学时要求,总学分要求,应取得证书,培养目标,计划年级"}, {"id": 5081, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4984, "classify_name": "计划课程数据", "level_id": 77, "label_ids": "31301&30510&31302&31518", "description": "", "label_names": "课程属性码,授课方式码,课程开设单位号,预修课程"}, {"id": 5082, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4984, "classify_name": "计划学分学时要求数据", "level_id": 77, "label_ids": "30063&30562&30056&30566", "description": "", "label_names": "学时要求,教学学时,学分要求,教学方式"}, {"id": 5083, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4985, "classify_name": "排课数据", "level_id": 78, "label_ids": "29415&30561&31360&30572&30571&31432&30323&29632&31304&29414&30582&30324&31020&31427&31428&30588&30587&30577&30514&30570&31298&30325", "description": "", "label_names": "上课班级号,教学地点,起始周,教学班名称,教学班号,选课校区号,开课学年度,修读人数,课程性质码,上课时间,教室所在校区号,开课学期码,终止周,选课人数限定,选课年级,教师工号,教师姓名,教学资源,排课要求,教学特征,课容量,开课说明"}, {"id": 5084, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4986, "classify_name": "选课数据", "level_id": 78, "label_ids": "31480&31433&30665&31431&30268&31429&29434&31430&30735", "description": "", "label_names": "院系要求,选课轮次码,是否重修,选课时间,年级要求,选课志愿,专业要求,选课日期,校区要求"}, {"id": 5085, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4987, "classify_name": "教室管理数据", "level_id": 77, "label_ids": "30580&31101&29606&30421&30586&30584&30687&31467&29608&29599&29601&30585&30299&30581&30569&30579&30583", "description": "", "label_names": "教室号,考试座位数,使用日期,所在楼层,教室类型码,教室用途,有效座位数,门牌号,使用节次,使用人号,使用人身份,教室管理部门,座位数,教室名称,教学楼号,教室占用情况码,教室描述"}, {"id": 5086, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4988, "classify_name": "教材基本数据", "level_id": 77, "label_ids": "29728&29729&30590&29730&30811&31070", "description": "", "label_names": "出版号,出版日期,教材名称,出版社,版次,编著者总数"}, {"id": 5087, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4989, "classify_name": "获奖教材数据", "level_id": 77, "label_ids": "31166&31171", "description": "", "label_names": "获奖届次,获奖教材编号"}, {"id": 5088, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4989, "classify_name": "教材编者数据", "level_id": 77, "label_ids": "31071&31067&31069&31068", "description": "", "label_names": "编著者角色码,编著者单位,编著者姓名,编著者号"}, {"id": 5089, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4989, "classify_name": "教学成果获奖数据", "level_id": 78, "label_ids": "31165&29467&31167&30941&31169&30565&30383&30564&31170&30125", "description": "", "label_names": "获奖名称,主要完成人,获奖年份,科技奖类别码,获奖成果名称,教学成果编号,成果获奖类别码,教学成果名称,获奖成果编号,完成人总数"}, {"id": 5090, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4989, "classify_name": "教学成果完成人数据", "level_id": 78, "label_ids": "30126&30127&30124&30123", "description": "", "label_names": "完成人角色码,完成单位,完成人姓名,完成人号"}, {"id": 5091, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4990, "classify_name": "评教数据", "level_id": 78, "label_ids": "31284&31285&31286&31283", "description": "", "label_names": "评教指标内容,评教要素,评教题型类别码,评教主体码"}, {"id": 5092, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4990, "classify_name": "教学评估信息数据", "level_id": 78, "label_ids": "30622&30884&29678&30887&30803&30255&31273&30346&29781&29570&30352&29676&29679&30974&29906&30598&30452&30973&29978&30667&30450&30611&30260&31460&29854&30647&30644&30617&29569&29857&31538&30885&30612&29855&29571&30639&30258&29858&29916&29856&30256&29859&30261&29907&30209&30546&29782&30508&29677&30259&29418&29779&30627&30621&30339&29780&30652&30254&31537&29552&30102&29720&30257&29846&30886&29989&31519", "description": "", "label_names": "是否双师型教师,省人大,全国政协,省政协证书或文件照片,派往国家,市人大证书或文件照片,证书或文件照片,录取时间,区政协,优秀教师级别,录取证书或文件照片,全国人大,全国政协证书或文件照片,第二资格证书照片,名师级别,文件或相关照片,批准部门,第二职称证书照片,填表年度,是否青年骨干教师,批准时间,是否享受政府津贴等优秀人才,带头人照片,鉴定或文件照片,双师型A型,是否有实验任务,是否教学名师,是否公派出国,优秀人才类别,双师型D型,骨干教师证书或文件照片,省人大证书或文件照片,是否优秀教师,双师型B型,优秀教师证书或文件照片,是否师德先进个人,师德先进个人级别,双师型E型,回国时间,双师型C型,市政协,双师型F型,带头人级别,名师证书或文件照片,岗位类别,攻读学位,区政协证书或文件照片,授权证书照片,全国人大证书或文件照片,师德先进个人证书或文件照片,专/兼职类别,区人大,是否学术技术带头人,是否参加人大政协,录取学校,区人大证书或文件照片,是否正在攻读博（硕）士学位,市人大,骨干教师级别,任职证书或文件照片,学科类别,出国时间,市政协证书或文件照片,参加工作时间,省政协,备注,预期毕业时间"}, {"id": 5093, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4991, "classify_name": "考试安排数据", "level_id": 78, "label_ids": "31104&31108&31100&30881&31109&31107&31105&31106&30882", "description": "", "label_names": "考试教室号,考试时长监考人工号,考试人数,监考人姓名,考试时间,考试时长,考试方式码,考试日期,监考人工号"}, {"id": 5094, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "教职工基础数据", "level_id": 78, "label_ids": "30337&30559&30602&29522&30664&30512&30653&30103&29845&29643&30713&30682&30032&30894&29558&30593&29755&29732&29674&29796&31373&30681&30680&30760&30648&30237&30758&31364&30821&31065", "description": "", "label_names": "当前状态码,教务开课教师号,文化程度码,从教年月,是否转业军人,排序,是否独生子女,学科类别码,参加工作年月,健康状况码,来校日期,最高职称级别码,婚姻状况码,研究方向,任课状况码,教职工类别码,删除标志,出生地码,入编日期,单位号,转业日期,最高学历码,最高学位码,档案编号,是否有犯罪记录,工号,档案文本,起薪日期,特长,编制类别码"}, {"id": 5095, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "个人通讯方式数据", "level_id": 79, "label_ids": "29769&29768&31003&31004", "description": "", "label_names": "办公校区楼号,办公房间号,紧急联系人,紧急联系人移动电话"}, {"id": 5096, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "奖励数据", "level_id": 79, "label_ids": "30012&31025&31026&30005&31517&30332&31029&30009&31173&31157&30008&31028&30011&30000&31164&31172&30334&31176&29974&30675&30620&29977&30001&30010&30002&30006&29970&29975&31175&30007&31027&30333&29976&30004&30003&30013", "description": "", "label_names": "奖励证书附件,经办人审批姓名,经办人审批结果,奖励学年度,颁奖单位,归档姓名,经办部门审批结果,奖励类型码,获奖类别码,荣誉称号码,奖励活动名称,经办部门审批时间,奖励证书内容,奖励代码,获奖原因,获奖日期,归档结果,获奖项目,填报日期时间,最后修改姓名,是否删除,填报部门审批结果,奖励内容,奖励级别码,奖励原因,奖励文号,填报人姓名,填报部门审批姓名,获奖角色码,奖励方式码,经办部门审批姓名,归档时间,填报部门审批时间,奖励名称码,奖励名称,奖励金额"}, {"id": 5097, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "语言能力数据", "level_id": 78, "label_ids": "30669&29991&29992&29452&29453", "description": "", "label_names": "普通话水平等级码,外国语种熟练程度码,外国语种码,中国语种熟练程度码,中国语种码"}, {"id": 5098, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "任课数据", "level_id": 78, "label_ids": "29562&29560&31312&29559&29563&29561&30509&29556", "description": "", "label_names": "任课课程类别码,任课角色码,课程类型码,任课终止年月,任课起始年月,任课课程号,授课人数,任课总学时"}, {"id": 5099, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "教学工作量数据", "level_id": 78, "label_ids": "30574&30563&30560&30578&30576&30573", "description": "", "label_names": "教学终止年月,教学工作量,教学内容,教学起始年月,教学评语,教学类型码"}, {"id": 5100, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "党政职务数据", "level_id": 79, "label_ids": "31321&29546&31119&29550&29651&29649&31112&31113&29653&29555&30824&29652&29465&29650&29548&29549&31117&29547&31115&30336", "description": "", "label_names": "负责人分类码,任职原因,职务类别码,任职日期,免职文号,免职原因码,职位分类码,职务变动类别码,免职日期,任职部门,状态码,免职方式码,主管或从事的工作,免职批准单位,任职文号,任职方式码,职务排序,任职批准单位,职务名称码,当前任职状况"}, {"id": 5101, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "管理工作数据", "level_id": 78, "label_ids": "30994&30996&30995&30991&30992", "description": "", "label_names": "管理终止年月,管理部门,管理起始年月,管理工作名称,管理工作量"}, {"id": 5102, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "专业技术职务数据", "level_id": 78, "label_ids": "31142&31141&31138&31226&31225&31140&31282&31139&29553&31281&31355&31143&31137&29881", "description": "", "label_names": "聘任证书照片,聘任职务级别,聘任情况码,解聘日期,解聘原因,聘任职务码,评审单位,聘任终止日期,任职资格名称码,评定日期,资格证书照片,聘任起始日期,聘任单位,取得资格途径码"}, {"id": 5103, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "研究生导师数据", "level_id": 77, "label_ids": "29543&29545&30198&30053", "description": "", "label_names": "任博导年月,任硕导年月,导师类别码,学位授予单位码"}, {"id": 5104, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "教师资格证数据", "level_id": 77, "label_ids": "31274&31354&31271&29544", "description": "", "label_names": "证书照片,资格种类,证书号码,任教学科"}, {"id": 5105, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "岗位证书数据", "level_id": 77, "label_ids": "30212&30668&31516&30213&30211&30214", "description": "", "label_names": "岗位证书照片,是否高校教师资格,颁发单位,岗位证书类型,岗位证书名称,岗位证书编号"}, {"id": 5106, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4992, "classify_name": "工人技术等级及职务数据", "level_id": 77, "label_ids": "30220&30975&30218&30977&30219", "description": "", "label_names": "工人技术职务码,等级发证机关,工人工种码,等级评定日期,工人技术等级码"}, {"id": 5107, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4993, "classify_name": "教职工考核数据", "level_id": 77, "label_ids": "31096&30594&29810&31095&30082&29811&30084&29809&30083&31091", "description": "", "label_names": "考核类别码,教职工考核单位号,单位考核评语,考核日期,学校考核结果码,单位考核负责人号,学校考核负责人号,单位考核结果码,学校考核评语,考核内容"}, {"id": 5108, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4993, "classify_name": "工人考技考工数据", "level_id": 77, "label_ids": "31086&31092&31090&30455&31087&31089&31088", "description": "", "label_names": "考工年月,考核工种码,考工部门,技术级别,考工成绩,考工评语,考工证书号"}, {"id": 5109, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4993, "classify_name": "应聘管理辅助数据", "level_id": 77, "label_ids": "31145&30291&29498&29500&30297&30720&31146&30296&29417&30721&30899&30750&30295&30523&30661&30522&30751&31147&30292&30293&30521&30752&30520&29499", "description": "", "label_names": "聘用单位评审意见,应聘备注,人事处经办人号,人事处经办日期,应聘者陈述,校内外专家评审意见,聘用单位评审日期,应聘者编号,与被推荐人关系,校内外专家评审日期,确定日期,校领导审核日期,应聘者姓名,推荐日期,是否聘用,推荐人意见,校领导工号,聘用单位评审负责人号,应聘岗位,应聘日期,推荐人姓名,校领导意见,推荐人单位,人事处经办意见"}, {"id": 5110, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4993, "classify_name": "聘用合同管理数据", "level_id": 78, "label_ids": "31150&30860&31038&31022&30987&29894&31151&31148&29789&30658&31149&30154&29897&30859&30206", "description": "", "label_names": "聘用合同编号,甲方签约人,结束日期,终止说明,签约日期,合同内容,聘用性质码,聘用合同名称,协议工资,是否续签,聘用合同类别码,实际终止日期,合同备注,甲方单位,岗位档次"}, {"id": 5111, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4993, "classify_name": "来源数据", "level_id": 77, "label_ids": "30712&30711&30716&29838&29840&29839&30715&29835&29833&30592&29834", "description": "", "label_names": "来单位日期,来单位原因,来源码,原工资总额,原教护龄津贴,原技术职务码,来源地区码,原单位名称,原从事学科码,教职工号,原党政职务码"}, {"id": 5112, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4993, "classify_name": "部门调动数据", "level_id": 78, "label_ids": "31014&31013&31448&31447&30245&30597", "description": "", "label_names": "组织内调出部门号,组织内调入部门号,部门调动日期,部门调动原因,工资转移日期,文书档案号"}, {"id": 5113, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4993, "classify_name": "离岗数据", "level_id": 77, "label_ids": "31398&30927&30926", "description": "", "label_names": "返岗日期,离岗日期,离岗原因码"}, {"id": 5114, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4993, "classify_name": "病休数据", "level_id": 79, "label_ids": "30874&30875&30300&30367&30873&30301", "description": "", "label_names": "病休诊断证明,病休起始日期,康复诊断医院,恢复工作日期,病休诊断医院,康复诊断证明"}, {"id": 5115, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4993, "classify_name": "编制异动数据", "level_id": 78, "label_ids": "30327&31063&31062", "description": "", "label_names": "异动后编制码,编制异动日期,编制异动原因"}, {"id": 5116, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4993, "classify_name": "工资结构数据", "level_id": 79, "label_ids": "30355&30246", "description": "", "label_names": "形成日期,工资金额"}, {"id": 5117, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4994, "classify_name": "离职数据", "level_id": 78, "label_ids": "30931&30930&30929", "description": "", "label_names": "离职日期,离职去向,离职原因码"}, {"id": 5118, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4994, "classify_name": "离退休数据", "level_id": 79, "label_ids": "30924&31043&31358&30934&29585&30932&31128&31007&31464&31126&30938&31066&30276&30937&30328&31420&31366&31421&31422&31006&30434&30939&31120&30034&30936&31442&29589&30933&30935", "description": "", "label_names": "福利补贴,继续教育,赡养老人,离退休费执行时间,住房租金,离退休工资合计,联系人手机号,累计收入,银行卡号,联系人姓名,离退类别码,编号,幼儿照护,离退后管理单位,异地安置地点,退休前部门,身份证与,退休工资合计,退休集体工工资合计,累计扣税,手机号,离退起始日期,职务职称,子女教育,离退后享受级别码,郑州银行账号,住房贷款,离退休类别,离退休费支付单位"}, {"id": 5119, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4994, "classify_name": "返聘数据", "level_id": 78, "label_ids": "31402&31399&31403&31400&31401&31404&31405", "description": "", "label_names": "返聘终止日期,返聘单位号,返聘起始日期,返聘岗位,返聘工作量,返聘酬金,返聘金来源"}, {"id": 5120, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4995, "classify_name": "国内专家数据", "level_id": 77, "label_ids": "29428&31144&30446&29443&30444&30445&29492", "description": "", "label_names": "专业技术职务码,聘用单位号,批准年月,专家类别码,批准单位,批准单位级别码,享受待遇"}, {"id": 5121, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4996, "classify_name": "社会兼职职务数据", "level_id": 77, "label_ids": "30903&30904&30902&30901", "description": "", "label_names": "社会兼职起始日期,社会兼职辞职原因码,社会兼职终止日期,社会兼职码"}, {"id": 5122, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4996, "classify_name": "学术团体兼职数据", "level_id": 77, "label_ids": "30066&30065&30070&30069&30067&30068&30071", "description": "", "label_names": "学术兼职职务,学术兼职终止日期,学术团体级别码,学术团体名称,学术兼职起始日期,学术兼职辞职原因码,学术团体隶属或主管单位"}, {"id": 5123, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4996, "classify_name": "企业兼职数据", "level_id": 77, "label_ids": "29564&29566&29565&29567", "description": "", "label_names": "企业兼职终止日期,企业兼职起始日期,企业兼职职务,企业名称"}, {"id": 5124, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4997, "classify_name": "国内进修学习数据", "level_id": 78, "label_ids": "30044&29951&30047&31408&29461&31409&29463&29952", "description": "", "label_names": "学习终止年月,在学单位,学习起始年月,进修班名称,主办单位,进修结果码,主办单位性质码,在学单位类别码"}, {"id": 5125, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4997, "classify_name": "出国（境）进修学习工作数据", "level_id": 78, "label_ids": "30138&31276&30178&30285&29722&30176&30039&30177&30040&29721&30461&29922&30451&29724&29723&30418", "description": "", "label_names": "实回国日期,证件有效期,审批日期,应回国日期,出国（境）日期,审批单位,学习工作内,审批文号,学习工作成绩,出国（境）国别码,护照号/通行证号,团组名称,批准期限,出国（境）经费来源码,出国（境）目的码,所去单位英文名称"}, {"id": 5126, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4998, "classify_name": "住房数据", "level_id": 78, "label_ids": "30320&29609&30402&29658&31396&30304&29587", "description": "", "label_names": "建筑面积,使用面积,房屋产权码,入住年月,迁出日期,建成日期,住房编号"}, {"id": 5127, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4999, "classify_name": "科技项目基本数据", "level_id": 79, "label_ids": "29416&31228&29892&31499&29444&29891&30865&31227&29466&30965&30430&31493&30426&29817&31039&30911&30864&30798&31520&31504&31506&30196&30326&31509&31498", "description": "", "label_names": "下达文号,计划完成日期,合作形式码,项目批准号,专项编号,合作国家/地区码,申报项目号,计划完成情况码,主管部门,立项日期,所属行业码,项目委托单位,所属主课题,单位角色码,结项日期,社会经济目标,申报日期,活动类型码,预期研究成果及形式,项目来源单位,项目级别码,密级码,开题日期,项目结题形式,项目执行状态码"}, {"id": 5128, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4999, "classify_name": "项目协作单位数据", "level_id": 79, "label_ids": "29787&29786&29801&29893", "description": "", "label_names": "协作单位类型码,协作单位,单位排名,合作方式"}, {"id": 5129, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4999, "classify_name": "项目经费数据", "level_id": 79, "label_ids": "31024&30494&31491&31230&31508&30528", "description": "", "label_names": "经办人姓名,拨付协作单位经费,项目凭证编号,计划经费总额,项目经费来源码,支出日期"}, {"id": 5130, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4999, "classify_name": "项目人员数据", "level_id": 79, "label_ids": "29554&29516&31076&31122&31223&30234&30775&29797", "description": "", "label_names": "任职资格名称码称码,人员类型,署名顺序,职称,角色码,工作量,每年工作月数,单位名称"}, {"id": 5131, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 4999, "classify_name": "项目合同信息数据", "level_id": 79, "label_ids": "29647&31314&30871&29473&29899&30861&30989&29445&30858&31236&29902&29446&29900&29904&30633&29472&31235&29903&29901&29896&29905", "description": "", "label_names": "免征营业税日期,课题分类码,电子附件,乙方负责人,合同开始日期,甲方负责人,签订地点,丙方,甲方,认定日期,合同类型码,丙方负责人,合同截止日期,合同编号,是否已归档,乙方,认定号,合同结束日期,合同签订日期,合同名称,合同金额"}, {"id": 5132, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5000, "classify_name": "科研机构基本数据", "level_id": 78, "label_ids": "31323&30706", "description": "", "label_names": "负责人姓名,机构类型"}, {"id": 5133, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5001, "classify_name": "科技成果人员数据", "level_id": 78, "label_ids": "30527&31325&30511&30419", "description": "", "label_names": "撰写字数,贡献率,排名/总人数,所在单位"}, {"id": 5134, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5001, "classify_name": "科技著作数据", "level_id": 79, "label_ids": "31186&31264&31189&29938&31190&31293&29731&31187", "description": "", "label_names": "著作中文名称,论著类别码,著作编号,图书出版号,著作英文名称,语种码,出版社级别码,著作字数"}, {"id": 5135, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5001, "classify_name": "科技论文基本数据", "level_id": 79, "label_ids": "30429&31241&30431&29693&31255&30104&31253&31250&31263", "description": "", "label_names": "所属技术领域,论文中文名称,所属项目编号,其他收录情况,论文英文名称,学科门类(科技)码,论文编号,论文类型码,论著收录情况码"}, {"id": 5136, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5001, "classify_name": "科技论文发表数据", "level_id": 78, "label_ids": "29876&29749&29830&30364&30266&30690&31363&29925&29748&29750", "description": "", "label_names": "发表日期,刊物级别码,卷号,总期号,年号,期号,起始页号,国家/地区码,刊物名称,刊物编号"}, {"id": 5137, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5001, "classify_name": "科技论文报告数据", "level_id": 78, "label_ids": "31261&29580&31246", "description": "", "label_names": "论文集名称,会议编号,论文报告形式码"}, {"id": 5138, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5001, "classify_name": "鉴定成果数据", "level_id": 79, "label_ids": "31457&31459&31463&31461&31458&30128&31462&30382", "description": "", "label_names": "鉴定单位名称,鉴定成果编,鉴定结论码,鉴定批文号,鉴定成果名,完成形式码,鉴定日期,成果类型码"}, {"id": 5139, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5001, "classify_name": "专利成果基本数据", "level_id": 78, "label_ids": "30868&29439&29438&29440&29487&29488&29436&30790&30447&30867&29937&29435&29442&29437&29441&30506&30505&30449", "description": "", "label_names": "申请编号,专利权人,专利成果编号,专利类型码,交纳专利年费日期,交纳金额,专利代理机构,法律状态码,批准形式码,申请名称,国际专利主分类号,专利代理人,专利证书编号,专利成果名称,专利终止日期,授权公告日期,授权公告号,批准日期"}, {"id": 5140, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5001, "classify_name": "专利出售数据", "level_id": 78, "label_ids": "29718&29719&29882&30702", "description": "", "label_names": "出售日期,出售金额,受售单位,本年实际收入"}, {"id": 5141, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5001, "classify_name": "技术转让基本数据", "level_id": 78, "label_ids": "30379&31324&29884&30986&30910&29883", "description": "", "label_names": "成交金额,负责人工号,受让方类型码,签定日期,社会经济效益码,受让方名称"}, {"id": 5142, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5001, "classify_name": "计算机软件著作权数据", "level_id": 78, "label_ids": "30708&30878&31268&30990&31384&31270&31188&31275&30707&31535", "description": "", "label_names": "权利范围,登记号,设计人,简称,软件著作证书编号,证书取得日期,著作权人,证书颁发机构,权利取得方式,首次发表日期"}, {"id": 5143, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5002, "classify_name": "学术会议数据", "level_id": 78, "label_ids": "29576&30625&29581&31127&31249&29471&29844&29577&29582&30820&30457&29462&29464&29578&29574&29579&29575", "description": "", "label_names": "会议名称,是否境内外,会议英文名称,联系人工号,论文篇数,举办单位,参加单位,会议地点,会议起始日期,特邀报告篇数,投入经费,主办单位号,主持人工号,会议等级码,会议举办形式码,会议终止日期,会议人数"}, {"id": 5144, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5002, "classify_name": "派出人员数据", "level_id": 78, "label_ids": "29482&29486&29485&29483&30802&30801&29484", "description": "", "label_names": "交流主题,交流结束日期,交流类型码,交流地点,派出单位,派出人号,交流开始日期"}, {"id": 5145, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5002, "classify_name": "接受人员数据", "level_id": 78, "label_ids": "30515&30516&30517", "description": "", "label_names": "接受人单位,接受人号,接受人姓名"}, {"id": 5146, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5003, "classify_name": "账务管理数据", "level_id": 78, "label_ids": "30945&30365&30954&30952&29624&30953&29775&30947", "description": "", "label_names": "科目名称,总科目属性码,科目编号,科目类别码,修改时间,科目级次码,助记符,科目年度"}, {"id": 5147, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5003, "classify_name": "部门考勤", "level_id": 78, "label_ids": "30877&29475&29696&29474&29510&31296&30876&31079&29489&29698&30436&29490&30030&29752&30616&31208&29509&29755&30683&29508&31077&30243&29507&30031&30280&31078&31531&31297", "description": "", "label_names": "病假条1,事假条,其它天数,事假天数,人事考勤说明,请假扣发,病假天数1,考勤合计(实发),产假天数1,其它条,扣发,产假条1,婚假天数,创建人姓名,是否全勤,补发,人事考勤补发说明,删除标志,月,人事考勤补发,考勤单位号,工资基数,人事考勤扣发,婚假条,应发,考勤单位名称,餐卡扣发,请假类型"}, {"id": 5148, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5004, "classify_name": "凭证类型数据", "level_id": 78, "label_ids": "31001&29712&31477&30999", "description": "", "label_names": "类型编号,凭证标记,限制科目,类型名称"}, {"id": 5149, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5004, "classify_name": "凭证数据数据", "level_id": 78, "label_ids": "30427&31238&29715&29709&30844&29735&29990&30530&29739&29713&31523&29714", "description": "", "label_names": "所属单位号,记账方向码,凭证金额,凭证摘要,生成日期,出纳人号,复核人号,支票号,分录标记码,凭证编号,领/交款人号,凭证说明"}, {"id": 5150, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5004, "classify_name": "凭证编号数据", "level_id": 78, "label_ids": "29711&29573&29572&29710", "description": "", "label_names": "凭证标志,会计期间,会计年度,凭证日期"}, {"id": 5151, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5004, "classify_name": "科目余额数据", "level_id": 79, "label_ids": "30948&30943&30949&30950&30946&30942&30944", "description": "", "label_names": "科目当月借方发生金额,科目全年借方累计金额,科目当月贷方发生金额,科目期末余额,科目年初余额,科目入账月份,科目全年贷方累计金额"}, {"id": 5152, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5005, "classify_name": "项目基本数据数据", "level_id": 78, "label_ids": "31521&30957&31505&31513&31365&31501&31502&31500&30129&31492&31510&31511&29597", "description": "", "label_names": "预算金额,科研项目编号,项目类别码,项目负责人号,超支限额,项目支出,项目收入,项目摘要,完成日期,项目名称,项目编号,项目联系人,余额"}, {"id": 5153, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5005, "classify_name": "项目经费来源数据", "level_id": 79, "label_ids": "30497&31454&30495&31455&30239&30496", "description": "", "label_names": "拨款数,重点学科经费,拨付协作经费,重点实验室经费,工程研究中心经费,拨入日期"}, {"id": 5154, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5005, "classify_name": "项目余额数据", "level_id": 79, "label_ids": "31490&31489&31494&31495&31503&31496", "description": "", "label_names": "项目全年贷方累计金额,项目入账年月,项目年初余额,项目当月借方发生金额,项目期末余额,项目当月贷方发生金额"}, {"id": 5155, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5006, "classify_name": "暂存款数据", "level_id": 79, "label_ids": "31524&29523&29762&29761&29524&29760&29763&29525", "description": "", "label_names": "领取人号,付款日期,到款理由,到款日期,付款说明,到款凭证号,到款金额,付款金额"}, {"id": 5156, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5006, "classify_name": "暂借款数据", "level_id": 79, "label_ids": "29634&30753&29637&29638&29636&29639", "description": "", "label_names": "借款凭证号,核销日期,借款理由,借款经手人号,借款日期,借款金额"}, {"id": 5157, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "工资基本信息数据", "level_id": 79, "label_ids": "29888&30241&29861&30610&29885&29887&30208&30448&29886&31192&29979&30134&31515&30242&30281&30440&31320", "description": "", "label_names": "变动金额,工资变动原因码,发工资年月,明细项目码,变动后岗位等级码,变动后薪级工资级别码,岗位等级码,批准文件号,变动后标准工资额,薪级工资级别码,增减级数,实发合计,项目金额,工资变动批准日期,应发合计,扣款合计,调资起薪日期"}, {"id": 5158, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "在编工资填报数据", "level_id": 79, "label_ids": "29972&31446&29501&29973&29502&30643&29503&30247&31445", "description": "", "label_names": "填报单位号,部门审批时间,人事审批,填报单位名称,人事审批姓名,是否操作,人事审批时间,工资项目代码,部门审批"}, {"id": 5159, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "非在编工资数据", "level_id": 79, "label_ids": "30548&30441&31295&30033&30723&31124&30443&30435&31381&29685&29953&31294&30728&30739&30118&30795&31123&30119&30439&31045&31044&30273&29590&30120&29766&30058&30925&31093&31319&30035&29586&29969&29513&31393&29655&31005&30805&30797&30845&30019&30113&30020&30114&31359&30264", "description": "", "label_names": "政府购买,扣社保,误餐扣发,婴幼儿照护累计,校内工资,职贴,扣除合计累计,扣医保,软件学院浮动津贴,公积金,地贴,误餐,校内补贴,校医补助,学院岗位津贴,津补贴合并项,职称工资,学院扣发,扣失业,绩效,继续教育累计,年终绩效,住房贷款累计,学院补发,副科津贴,学历工资,福补,考核工资,调标,子女教育累计,住房租金累计,基础工资,人员类别,辅导员岗贴,党支部书记津贴,累计扣发合计,浮动津贴,津贴,生活补贴,奖金,学院副科津贴,女工补助,学院实发,赡养老人累计,年"}, {"id": 5160, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "工资奖金数据", "level_id": 79, "label_ids": "30270&30272&30269&30271", "description": "", "label_names": "年终奖基数,年终奖扣税合计,年终奖合计,年终奖实发合计"}, {"id": 5161, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "在编岗贴工资数据", "level_id": 79, "label_ids": "31443&30812&31116&29997&30137&30283&30432&29514&29588&30021&30814&30442&30289&30221&29785&29584&31121&31231&30217&30136&30135&30603&29480&30850&31046&30215&29995&30727&30719&31465&30216&30248&29968&29512&31040&30472&31191&30796&29504&31008&30207&31370&31213&29450&31111&30437&31444&31221&29617&29699&31209&31009&29648&31041&31539", "description": "", "label_names": "部门代码,物业补贴,职务工资,失业保险,实发绩效二月份,应发工资,所得税,人员类别代码,住房补贴,女职工补助,特岗津贴,扣缴纳公积金个人承担,应税所得,工伤保险,医疗保险,住房公积金,职工号,计税方案,岗贴收入合计,实发工资,实发岗贴,文明奖,交互津贴,生育保险,绩效18年校内部分,岗贴,外教所得税,校内补助值班费,校内,银行账号,岗贴所得税,差额,基本工资小计,人员序号,统发应税所得,抵扣合计累计,薪级工资,津补贴合计,人事应发工资,累计收入合计,岗位津贴,身份证号,补贴,个人所得税,职业年金,扣发合计,部门名称,见习工资,保留福利,养老保险,补发工资,累计税,免征额累计,统发所得税,高层次住房补贴"}, {"id": 5162, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "辅导员浮动工资数据", "level_id": 79, "label_ids": "31047&30524&29506&29743&31202&29495&30816&30774&31201&30806&31200&29744&30815&31199&31394&31118&30438&30153&31203", "description": "", "label_names": "绩效工资,提交状态,人事经办人审批结果,分管姓名,行政领导审核,人事分管审批结果,特教人数,正常人数,行政领导姓名,浮动绩效,行政级别审批时间,分管审批时间,特情说明,行政级别审批姓名,辅导员浮动工资,职务类别,扣发说明,实际管理人数,行政领导结果"}, {"id": 5163, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "软件学院浮动工资数据", "level_id": 79, "label_ids": "31449&30800&31382&30676&29505&29988&31210&29987&31450&30282&29615&31064&30673&30799", "description": "", "label_names": "部门负责人姓名,活动缺勤说明,软件浮动工资,最后修改时间,人事经办人姓名,处长审批时间,补发说明,处长姓名,部门负责人审批结果,应发合计说明,保卫特岗津贴,编制类别,最后修改人姓名,活动缺勤次数"}, {"id": 5164, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "教职工劳务补贴数据", "level_id": 79, "label_ids": "31217&29870&31215&31218&31219&30535", "description": "", "label_names": "补贴税前金额,发放部门号,补贴发放日期,补贴税后金额,补贴说明,收入名称"}, {"id": 5165, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "税率登记数据", "level_id": 78, "label_ids": "30961&30879&31362&30880", "description": "", "label_names": "税率（%）,登记说明,起始日期,登记金额"}, {"id": 5166, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "教职工缴税数据", "level_id": 78, "label_ids": "31042&30244&29697", "description": "", "label_names": "统计生成日期,工资所得税总计,其它所得税总计"}, {"id": 5167, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "学生收费数据", "level_id": 78, "label_ids": "30140&30290&30541&30538&31426&30928&29663&30537", "description": "", "label_names": "实缴金额,应缴金额,收费项目代码,收费区间名称,退费金额,离校年度,入学年度,收费区间代码"}, {"id": 5168, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "学生应收合计数据", "level_id": 78, "label_ids": "30286&31316", "description": "", "label_names": "应收合计,调整合计"}, {"id": 5169, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "收费项目数据", "level_id": 78, "label_ids": "30543&30542&30544", "description": "", "label_names": "收费项目备案批文号,收费项目名称,收费项目编号"}, {"id": 5170, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5007, "classify_name": "学生欠费数据", "level_id": 78, "label_ids": "30770&30768&30769", "description": "", "label_names": "欠费项目编号,欠费学年,欠费金额"}, {"id": 5171, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5008, "classify_name": "票据管理数据", "level_id": 78, "label_ids": "30922&30923&30335&30688&30857&29481&31527&31134&29873&29675&30388&30139&29913&30253&29598&29874&31528&31536&31456", "description": "", "label_names": "票据备注,票本序号,归还日期,有效张数,用途,交回人号,领用日期,联系方法,发票种类码,入账科目及项目,截止号,实开金额,启始票号,已用截止号,作废张数,发票规格码,领用部门号,验收人号,金额限制"}, {"id": 5172, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5008, "classify_name": "标题管理数据", "level_id": 78, "label_ids": "30717", "description": "", "label_names": "标题名称"}, {"id": 5173, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5009, "classify_name": "学校用地数据", "level_id": 77, "label_ids": "29827&30856&29949&30736&29880&30855&29828&29948&29879&29950", "description": "", "label_names": "占地用途码,用地编号,土地使用状况码,校区规划图,取得日期,用地地址,占地面积,土地产权码,取得方式码,土地证号"}, {"id": 5174, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5010, "classify_name": "学校建筑物基本数据", "level_id": 77, "label_ids": "30403&30413&30314&30310&30318&30958&30959&30417&30415&30316&30453&31239&29611&30408&30303&30459&30313&30406&30416&30311&30410&31269&30404&30317&29612&30649&30357&30414&30192&30412&30122&30312&30405&30710&30315&30363&30319&30411&30686&31032&30460&30309", "description": "", "label_names": "房间使用单位号,房间朝向,建筑物地址,建筑物占地面积,建筑物状况码,租赁结束日期,租赁起始日期,房间管理单位号,房间用途码,建筑物平面图,承租单位名称,记间性别码,供热状况,房间备注,建成年月,抗震设防标准码,建筑物图片,房间号,房间电话,建筑物号,房间平面结构图,设计使用年限,房间使用状态码,建筑物投资总额,供电情况,是否有通风设施,总使用面积,房间楼层,容纳人数,房间是否有空调,安防设施,建筑物名称,房间使用面积,权属证号,建筑物层数,总建筑面积,建筑物结构码,房间建筑面积,月租金,经费来源码,抗震设防烈度码,建筑物分类码"}, {"id": 5175, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5010, "classify_name": "建筑物房间数据", "level_id": 77, "label_ids": "29640&30278&30409&30766&30623&30709&30767&30407&30765", "description": "", "label_names": "值班电话,床位数,房间姓别,楼房号,是否可用,权属单位名称,楼房名称,房间名称,楼层"}, {"id": 5176, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5010, "classify_name": "建筑物修缮数据", "level_id": 77, "label_ids": "29628&29630&29631&29626&29625&29627&31023&29629", "description": "", "label_names": "修缮开始日期,修缮结束日期,修缮说明,修缮出资单位,修缮内容,修缮单位,经办人号,修缮经费"}, {"id": 5177, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5010, "classify_name": "实验室基本数据", "level_id": 77, "label_ids": "30308&30161&30158&30160&30162&30166&30159&30164&30993&29915&30165&31322", "description": "", "label_names": "建立日期,实验室名称,实验室介绍,实验室号,实验室所属学科,实验室类别码,实验室位置,实验室电话,管理级别码,器材管理员号,实验室简称,负责人号"}, {"id": 5178, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5010, "classify_name": "实验室运行数据", "level_id": 77, "label_ids": "30529&30167&30458&31397", "description": "", "label_names": "支出额,实验室费用编码,投入额,运行说明"}, {"id": 5179, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5010, "classify_name": "实验室评估数据", "level_id": 77, "label_ids": "31280", "description": "", "label_names": "评估结果码"}, {"id": 5180, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5010, "classify_name": "实验室人员数据", "level_id": 77, "label_ids": "30619&30210&30204", "description": "", "label_names": "是否兼职,岗位职称,岗位人数"}, {"id": 5181, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5010, "classify_name": "实验项目数据", "level_id": 77, "label_ids": "31174&29420&30172&30155&30262&30156&31482&30169&30168&30171&30173&30170&30776", "description": "", "label_names": "获奖级别码,专业分类码,实验者类别码,实验名称,带课教师号,实验地点,隶属课程号,实验时数,实验序号,实验者人数,实验要求码,实验类别码,每组人数"}, {"id": 5182, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5011, "classify_name": "仪器设备基本数据", "level_id": 77, "label_ids": "29717&29792&29895&29534&29533&30840&29746&29754&29528&29604&31333&29539&31267&29526&29819&29491&29527&29613&29540&29536&31331&29945&30656&29614&29872&31265&29542&30839&29535&29716&29530&30454&29529&29607&30077&30838&31266&29532&29531&31330", "description": "", "label_names": "出厂日期,单价,合同号,仪器英文名称,仪器数量,生产国别地区码,分类名称,创建者,仪器名称,使用方向名称,购置日期,仪器说明,设备负责人姓名,仪器价格,单据号,产权码,仪器号,供货商,仪器配置,仪器计量单位,财务财号,图片,是否精密贵重仪器,保修截止日期,发票号,设备来源,价格币种,生产国别地区名称,仪器规格,出厂号,仪器地点,技术指标,仪器品牌,使用状况码,学校单位层次码,生产国别/地区码,设备负责人号,仪器地点名称,仪器地点号,财务帐号"}, {"id": 5183, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5011, "classify_name": "仪器设备附件数据", "level_id": 77, "label_ids": "31471&31468&31470&29517&31472&31473&31469", "description": "", "label_names": "附件数量,附件价格,附件型号规格,人民币总价,附件编号,附件英文名称,附件名称"}, {"id": 5184, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5011, "classify_name": "仪器设备管理数据", "level_id": 77, "label_ids": "29603&31526&31060&30955&30321&29605&29600&30567&29618&30828&31030&30912&30322&31525", "description": "", "label_names": "使用单位号,领用单位,维护人数,科研时数,开设实验个数,使用方向码,使用人时数,教学时数,保管人号,现状码,经手人号,社会项目时数,开设实验时数,领用人号"}, {"id": 5185, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5011, "classify_name": "精密贵重仪器设备使用数据", "level_id": 77, "label_ids": "30726&30540&30893&30288&29962&30545&30725&29964&30568&29890&30607&29602&31055&30606&30743&29959&29963&29841&31410&29960&30062&31530&29694&30130&29929&30742&29691", "description": "", "label_names": "校内测试金额,收费标准,知名用户,应用成果,培训教师,改进实验,校内测样数,培训研究生,教学机时,可供机时日程表,新开实验,使用功能数,维修次数,新开发功能数,校外测试金额,培训专科生,培训本科生,原有功能数,进行科研课题,培训其他人,学年,额定机时,其他机时,完成科研课题,国家级奖数,校外测样数,其他奖励数"}, {"id": 5186, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5011, "classify_name": "仪器设备维修数据", "level_id": 77, "label_ids": "31049&31056&30551&31052&31051&31054&30554&30553&30552&31050&31057&31522&31031&31053&30174&31058&31059", "description": "", "label_names": "维修内容,维修申报人,故障原因,维修完成日期,维修商,维修日期,故障现象,故障发生时间,故障发生日期,维修单位,维修结论,预计维修工时,经费来源,维修方式,审批人,维修费用,维修预算"}, {"id": 5187, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5011, "classify_name": "仪器设备报损、报废数据", "level_id": 77, "label_ids": "30465&29986&30464&30501", "description": "", "label_names": "报损报废日期,处理结果,报损报废原因,损废说明"}, {"id": 5188, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5011, "classify_name": "软件资源数据", "level_id": 77, "label_ids": "30121&30810&29792&30507&31033&31383&31380&29519&29961&31379&29520", "description": "", "label_names": "安装调试费,版本号,单价,授权范围,经费科目码,软件编号,软件名称,介质数量,培训及技术支持费,软件分类码,介质类型"}, {"id": 5189, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5012, "classify_name": "档案机构数据", "level_id": 77, "label_ids": "30761&30762&30759", "description": "", "label_names": "档案馆代码,档案馆名称,档案机构类别"}, {"id": 5190, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5012, "classify_name": "全宗数据", "level_id": 77, "label_ids": "29681", "description": "", "label_names": "全宗名称"}, {"id": 5191, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5012, "classify_name": "立档单位数据", "level_id": 77, "label_ids": "30963&30755", "description": "", "label_names": "立档单位号,档单位名称"}, {"id": 5192, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5012, "classify_name": "案卷数据", "level_id": 77, "label_ids": "31533&29470&29690&30525&31529&31488&30998&30823&31476&29619&29680&29745&30705&30756&29541&30267&31021", "description": "", "label_names": "馆（室）编案卷号,主题词,关键词,摘要,题名,页数,类别号,状态,附注,保管期限,全宗号,分类号,机构或问题,档号,件数,年度（年份、年）,终止日期"}, {"id": 5193, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5012, "classify_name": "文件数据", "level_id": 78, "label_ids": "31436&31292&31002&29621&30962&30275&29767&30605&30600&31332&31532&31487&30195&29468&29695&30456&29616&30608&31152&29511&30813&30601", "description": "", "label_names": "逻辑地址,语种,紧急程度,信息系统描述,稿本,并列题名,副题名,文种,文件组合类型,责任者,馆(室)编件号,页号,密级,主送,其他责任者,抄送,保密期限,日期,脱机载体存址,人名,物理地址,文件编号"}, {"id": 5194, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5012, "classify_name": "文档数据", "level_id": 77, "label_ids": "31451&31232&29747&30754&30808&31222&29753&31272&31233&30609&29832&29682&30985&30599&31385&31154&30870&30819&30984&30704&30604", "description": "", "label_names": "采样频率,计算机文件名,分辨率,格式信息,源文件,规格,创建方法,证书引证,计算机文件大小,时间长度,压缩方式,全文信息,签名规则,文件档号,载体类型,色彩模式,电子文件类型,特殊载体编号,签名算法标识,机器型号,文档序号"}, {"id": 5195, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5013, "classify_name": "人员信息数据", "level_id": 78, "label_ids": "30724&30023&29451&29515&31369", "description": "", "label_names": "校内机构编号,姓名,个人职位,人员类别建议,身份证件类型码"}, {"id": 5196, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5013, "classify_name": "机构信息数据", "level_id": 77, "label_ids": "30741", "description": "", "label_names": "校外机构编号"}, {"id": 5197, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5014, "classify_name": "业务实体数据", "level_id": 77, "label_ids": "29449&31194&31197&31196&29448&29447&31195", "description": "", "label_names": "业务行为,行为依据,行为时间,行为日期,业务状态,业务人员编号,行为描述"}, {"id": 5198, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5014, "classify_name": "实体关系数据", "level_id": 77, "label_ids": "29687&29689&29686", "description": "", "label_names": "关系描述,关系类型,关系"}, {"id": 5199, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5015, "classify_name": "工作简历数据", "level_id": 78, "label_ids": "30223&30670&30231&30230&30228&30222&30671&30233", "description": "", "label_names": "工作单位,曾任专业技术职务码,工作证明人,工作终止日期,工作简历备注,工作内容,曾任党政职务,工作起始日期"}, {"id": 5200, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5015, "classify_name": "政治面貌数据", "level_id": 78, "label_ids": "30329&31082&30550&29847&30279&31016&31083&30330&30133&31085&29850&31376&31019&29848&30533&31374&30532&30075&31378&29518&31375&31377&30534&31084&29623&29966&29967&30531&30691&30331&31081&31017&30238", "description": "", "label_names": "异常原因,考察（考核）意见,政治面貌码,参加所在单位,应交党费,组织部审核,考察（考核）日期,异常日期,实交党费,考察（考核）组织及考察人,参加日期,转出单位,组织部票据号,参加所在单位位,支部审核最后修改时间,转入前单位,支部审核人姓名,学校党组织代码,转正日期,介绍人,转入日期,转出日期,支部职务,考察（考核）类别码,修改人姓名,基数年,基数月,支部审核,未交党原因,异常类别码,考察（考核）,组织部审核人姓名,工号/学号"}, {"id": 5201, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5016, "classify_name": "通用通讯", "level_id": 79, "label_ids": "31441&30960&29829&31434&30872&30869&31074", "description": "", "label_names": "邮政编码,移动电话,即时通讯号,通信地址,电话,电子信箱,网络地址"}, {"id": 5202, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5016, "classify_name": "家庭通讯方式数据", "level_id": 79, "label_ids": "30394&30187&30186&30826&30190&30396&30188", "description": "", "label_names": "户口所在地,家庭电子信箱,家庭住址,现住址,家庭邮政编码,户口类别码,家庭电话"}, {"id": 5203, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5017, "classify_name": "家庭成员数据", "level_id": 79, "label_ids": "30380&30657&30787&29521&29688&30381&31368&29733", "description": "", "label_names": "成员工作单位,是否紧急联系人,民族码,从业码,关系码,成员工作单位或现居住地址,身份证件类型,出生年月"}, {"id": 5204, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5017, "classify_name": "家庭经济情况数据", "level_id": 79, "label_ids": "30201&30189&31357&29924&30183&30184&29776&30613&29923&30185", "description": "", "label_names": "就学地低保线,家庭类别码,赡养人口,困难程度码,家庭主要收入来源,家庭人口,劳动力人口,是否低保,困难原因码,家庭人均月收入"}, {"id": 5205, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5018, "classify_name": "总药房入库数据", "level_id": 78, "label_ids": "29670&29946&29947&29610&29673&29671&31315&29668", "description": "", "label_names": "入库日期,图片ID,图片code,供应商,入库金额,入库类型(采购入库,调拨入库),入库单编号"}, {"id": 5206, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5018, "classify_name": "药品库存", "level_id": 78, "label_ids": "29667&31161&31452&31453&31162&31160&31159&30249&29726&30250&29765&29764&29727&29669", "description": "", "label_names": "入库价格,药品总库存,采购入库数量,采购入库金额,药品编号,药品总价格,药品名称,已买数量,出库数量,已买金额,剩余金额,剩余数量,出库金额,入库数量"}, {"id": 5207, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5018, "classify_name": "就诊数据", "level_id": 79, "label_ids": "31424&31348&30829&29875&29458&29784&30823&29725&30274&29783&30061&30592&30536&30356&30366&31423&30202&30940&31425&29402&31158", "description": "", "label_names": "退款原因,费别,现金支付金额,发药,临床诊断,医师,状态,出库单编号,年龄,医保报销金额,学号,教职工号,收费,性别,总金额,退款人,就诊单编号,科室,退款时间,rp,药品单号"}, {"id": 5208, "gmt_create": "2024-09-09T11:05:17", "gmt_modified": "2025-07-24T11:41:36", "creator": "ywj", "owner": "public", "tp_id": 159, "parent_id": 5018, "classify_name": "药品详情数据", "level_id": 77, "label_ids": "30841&31163&29460&30842&30764&29672&31411&30689&30837", "description": "", "label_names": "生产批号,药房名称,临期设置,生产日期,检索码,入库编号,进货价,有效日期,生产厂家"}]}}