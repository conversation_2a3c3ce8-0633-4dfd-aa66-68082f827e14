{"code": 200, "message": "success", "data": {"error_info": "", "count": {"total": 10}, "datas": [{"tp_id": 162, "label_count": 653.0, "tp_name": "智能联网汽车", "subclass_count": "共有6个一级子类\n共有25个二级子类\n共有122个三级子类"}, {"tp_id": 161, "label_count": 189.0, "tp_name": "能源大数据", "subclass_count": "共有6个一级子类\n共有62个二级子类\n共有117个三级子类"}, {"tp_id": 160, "label_count": 357.0, "tp_name": "卷烟制造业", "subclass_count": "共有5个一级子类\n共有20个二级子类\n共有74个三级子类"}, {"tp_id": 159, "label_count": 2138.0, "tp_name": "教育行业", "subclass_count": "共有9个一级子类\n共有52个二级子类\n共有190个三级子类"}, {"tp_id": 158, "label_count": 625.0, "tp_name": "水利行业", "subclass_count": "共有6个一级子类\n共有38个二级子类\n共有172个三级子类"}, {"tp_id": 153, "label_count": 472.0, "tp_name": "政务行业", "subclass_count": "共有20个一级子类\n共有110个二级子类\n共有400个三级子类"}, {"tp_id": 151, "label_count": 275.0, "tp_name": "电信行业", "subclass_count": "共有19个一级子类\n共有20个二级子类\n共有64个三级子类"}, {"tp_id": 149, "label_count": 1055.0, "tp_name": "金融行业分类", "subclass_count": "共有4个一级子类\n共有14个二级子类\n共有76个三级子类"}, {"tp_id": 145, "label_count": 96.0, "tp_name": "个人信息安全", "subclass_count": "共有13个一级子类\n共有0个二级子类\n共有0个三级子类"}, {"tp_id": 148, "label_count": 20945.0, "tp_name": "医疗行业分类", "subclass_count": "共有8个一级子类\n共有27个二级子类\n共有103个三级子类"}]}}