{"db.connection.title": "数据库管理", "db.connection.connection": "连接管理", "db.connection.delete.archiveConnection.tip": "确认删除此归档连接吗？", "db.connection.test.connection": "测试连接", "db.connection.keepOpenDialog": "弹窗保持开启", "db.connection.available": "连接可用", "db.connection.update.approverGroup": "关联连接[{{boundArr}}]已有审批人组，是否以本次更改为最终设置？", "db.connection.noDataSource": "暂无可创建数据源类型", "db.connection.guide": {"okText": "立即建立", "cancelText": "忽略", "content": "新建连接，将连接纳管至平台，即可在平台上进行管理"}, "db.connection.create": "新建连接", "db.connection.noPerm": "您当前的角色是[{{roleNameList}}]，对[连接管理]没有操作权限", "db.connection.list": "连接列表:", "db.connection.sdt": {"group": "分组视图", "instance": "实例视图", "approver": "审批人组视图"}, "db.connection.approverGroup.delete.signal": "确认删除{{groupName}}？", "db.connection.approverGroup.new": "新建审批人组", "db.connection.noPerm2": "只有 DBA 或 高级用户 角色才能创建连接", "db.connection.sdt.deleteGroup.tip": "确认解除组吗？", "db.connection.sdt.moveoutConn.tip.title": "确认移出连接吗？", "db.connection.sdt.moveoutConn.tip.content1": "开启后移出分组的连接会清空此前的相关设置，包括权限设置、数据保护设置等", "db.connection.sdt.moveoutConn.tip.extra2": "是否清空连接上相关设置", "db.connection.sdt.addConn": "添加连接", "db.connection.sdt.addGroup": "新建分组", "db.connection.sdt.moveoutConn": "移出连接", "db.connection.sdt.copyConn": "复制连接", "db.connection.sdt.editConn": "编辑连接", "db.connection.sdt.testConn": "测试连接", "db.connection.sdt.deleteConn": "删除连接", "db.connection.sdt.setManager": "设置连接管理员", "db.connection.sdt.editName": "修改名称", "db.connection.resetPerm.title": "是否继承分组上相关设置", "db.connection.resetPerm.content": "选择继承后新添加进分组的连接会继承分组此前上的设置，包括权限设置、数据保护设置等", "db.connection.sdt.relieve": "解除", "db.connection.sdt.clearConnSetting.title": "是否清空连接上相关设置", "db.connection.sdt.clearConnSetting.content": "选择清空后，移出分组的连接会清空此前的相关设置，包括权限设置、数据保护设置等", "db.connection.group.addConn.success": "添加连接成功", "db.connection.group.moveoutConn.success": "移出连接成功", "db.connection.group.groupName.empty": "组名不能为空！", "db.connection.group.modifyGroupName": "修改分组名称", "db.connection.group.groupName": "分组名称", "db.connection.group.groupName.hint": "请输入分组名", "db.connection.group.connectionIds": "选择连接", "db.connection.group.connectionIds.hint": "请选择连接", "db.connection.group.inheritToken": "是否继承分组上相关设置", "db.connection.group.connectionIds.extra": "开启后新添加进分组的连接会继承分组此前的相关设置，包括权限设置、数据保护设置等", "db.connection.group.moveoutIsClear": "是否清空连接上相关设置", "db.connection.group.moveoutIsClear.extra": "开启后移出分组的连接会清空此前的相关设置，包括权限设置、数据保护设置等", "db.connection.conn.create": "创建", "db.connection.conn.monitorSwitch": "监控开关", "db.connection.conn.connSuccess": "{{val}}连接成功", "db.connection.conn.connAvailable": "连接可用", "db.connection.conn.connFail": "连接失败", "db.connection.conn.openAllSchema": "是否开启所有业务库", "db.connection.conn.filterResources": "选择过滤配置文件中定义的系统库", "db.connection.conn.isAdminWithAllPerms": "获取此连接所有访问权限", "db.connection.conn.userName": "用户名", "db.connection.conn.userName.plac": "请输入用户名", "db.connection.conn.password": "密码", "db.connection.conn.password.plac": "请输入密码", "db.connection.conn.principal.plac": "请输入Principal", "db.connection.conn.keytab.plac": "请上传Keytab文件(.keytab)", "db.connection.conn.krb5_config": "请上传Krb5 config文件(.conf)", "db.connection.conn.zk_principal.plac": "请输入ZK Principal", "db.connection.conn.edit.title": "{{conn}}连接", "db.connection.conn.panel1": "填写连接信息", "db.connection.conn.panel2": "连接配置", "db.connection.conn.pool.setting.success": "连接池配置成功", "db.connection.conn.panel2.pageRows.success": "查询时分页行数设置成功", "db.connection.conn.panel2.workConfig.success": "事务设置成功", "db.connection.conn.panel2.rowNum.hint": "查询时分页行数只允许输入正整数", "db.connection.conn.panel2.rowNum.hint2": "查询时分页行数范围为100~2000", "db.connection.conn.panel2.workConfig": "事务设置", "db.connection.conn.panel2.workConfig.auto": "允许自动提交", "db.connection.conn.panel2.workConfig.manual": "不允许自动提交", "db.connection.conn.panel2.pool": "连接池配置", "db.connection.conn.panel2.otherSetting": "其他配置", "db.connection.conn.panel2.pageRows": "查询时分页行数", "db.connection.conn.panel2.pageRows.plac": "请输入查询时分页行数", "db.connection.conn.formItem.plac": "请选择{{label}}", "db.connection.conn.formItem.required": "{{label}}不能为空", "db.connection.conn.formItem.plac2": "请选择或添加{{label}}", "db.connection.conn.implicitCommitTimeout.hint": "请输入合理的正整数", "db.connection.conn.implicitCommitTimeout.hint2": "请输入大于等于60且小于等于86400的整数", "db.connection.conn.sqlBackupStatus": {"success": "备份配置成功", "label": "数据备份", "extra": "仅支持对delete、update、drop、truncate操作进行数据备份"}, "db.connection.conn.sqlFlashBackSetting": {"label": "SQL闪回功能", "extra": "开启闪回功能的前提是开启数据备份"}, "db.connection.conn.sqlBackupLocation": "备份位置", "db.connection.conn.sqlBackUpTime": "备份时间", "db.connection.conn.backupConfig": "备份配置", "db.connection.conn.sqlBackUpTime.hint": "请选择备份时间", "db.connection.conn.secretKey": "变更密钥", "db.connection.conn.secretKey.tip": "不能为空", "db.connection.conn.approver.updateSuccess": "更改成功", "db.connection.conn.approver.connManager": "连接管理员", "db.connection.conn.approve": "审批人组", "db.connection.conn.approve.plac": "请选择审批人组", "db.connection.conn.approve.new": "新建审批人组", "db.connection.conn.batchAddConn": "批量添加连接", "db.connection.conn.connManager.success": "管理员更新成功", "db.connection.conn.connManager.org": "组织架构", "db.connection.approver.new": "新建审批人组", "db.connection.approver.edit": "编辑审批人组", "db.connection.approver.editUser": "编辑审批人组用户", "db.connection.approver.groupName.plac": "请输入审批人组名称", "db.connection.approver.groupName": "审批人组名称", "db.connection.approver.users": "审批人组用户", "db.connection.approver.users.plac": "请选择审批人组用户", "db.connection.approver.connections": "关联连接", "db.connection.approver.connections.plac": "请选择关联连接", "db.connection.approver.removeUser.tip": "确定移除该用户？", "db.connection.approver.connectionType": "数据库类型", "db.connection.approver.connectionName": "连接名称", "db.connection.approver.connectionUrl": "链接地址", "db.connection.approver.ruleTemplateName": "模板名称", "db.connection.approver.SID": "SID/服务名", "db.connection.approver.userName": "用户名", "db.connection.approver.manager": "连接管理员", "db.connection.approver.creator": "创建人", "db.connection.approver.createDateTime": "创建时间", "db.connection.approver.delete.tip": "确定移除？", "db.connection.tabs.archive.tip": "归档连接无法查看", "db.connection.tabs.archive": "已归档", "db.connection.tabs.overview": "连接概览", "db.connection.tabs.overview.guide.content1": "请确保相应资源已经开启，否则无法进行授权管理等操作", "db.connection.tabs.overview.guide.setting": "立即前往设置", "db.connection.tabs.overview.resourceManagement": "资源管理", "db.connection.tabs.overview.poolManagement": "连接池管理", "db.connection.tabs.overview.connectionMode.replicaSet": "副本集模式", "db.connection.tabs.overview.userAndConnCount": "用户数量/连接数量", "db.connection.tabs.overview.sqlAndExecuteCount": "今日SQL/执行总数", "db.connection.tabs.overview.logicalBase": "逻辑库", "db.connection.tabs.overview.tableCount": "表数量", "db.connection.tabs.overview.dataSourceType": "数据源类型", "db.connection.tabs.resourceManagement.note.hint": "最长50个字符！", "db.connection.tabs.resourceManagement.note.success": "备注修改成功", "db.connection.tabs.resourceManagement.note.updateTitle": "备注修改", "db.connection.tabs.resourceManagement.dbName": "资源名称", "db.connection.tabs.resourceManagement.dbName.plac": "请输入资源名称", "db.connection.tabs.resourceManagement.admin": "管理员", "db.connection.tabs.resourceManagement.enableStatus": "状态", "db.connection.tabs.resourceManagement.note": "备注", "db.connection.tabs.resourceManagement.closeTip": "确认关闭此资源吗？", "db.connection.tabs.poolManagement.totalConnectTime": "累计连接时长", "db.connection.tabs.poolManagement.totalConnectTime.tip": "jdbc与数据库持续连接的时长", "db.connection.tabs.poolManagement.totalRunTime": "运行时长", "db.connection.tabs.poolManagement.totalRunTime.tip": "用户占用连接时长", "db.connection.tabs.poolManagement.option": "状态管理", "db.connection.tabs.poolManagement.allSuspend.tip": "确认全部终止？", "db.connection.tabs.poolManagement.allSuspend": "全部终止", "db.connection.tabs.poolManagement.explainPlan": "执行计划", "db.connection.tabs.poolManagement.sql.identify": "【{{index}}}】", "db.connection.archiveConnection.tip": "确认归档此连接吗？", "db.connection.archiveConnection.success": "归档成功", "db.connection.unfile.tip": "确认撤销归档此连接吗？", "db.connection.unfile.success": "撤销归档成功", "db.connection.undoProblem.success": "撤销问题成功", "db.connection.batchDelete.tip": "确认批量删除吗？", "db.connection.batchDelete.success": "批量删除成功", "db.connection.batchTest": "批量测试", "db.connection.batchArchive": "批量归档", "db.connection.batchUndoProblem": "批量撤销问题", "db.connection.batchUnfile": "批量撤销归档", "db.connection.undoProblem": "撤销问题", "db.connection.unfile": "撤销归档", "db.connection.copyConn.tip": "只有 DBA 或 高级用户 角色才能复制连接", "db.connection.groupName": "实例名称", "db.connection.createUser": "创建人：", "db.connection.nodeName": "实例地址：", "db.connection.serviceName": "sid/服务名：", "db.connection.search": "请输入需要检索的内容", "db.connection.allConnectionsTab": "活跃连接", "db.connection.problemConnectionsTab": "问题连接", "db.connection.archiveConnectionTab": "归档连接", "db.auth.guide.databaseObject": "数据库对象", "db.auth.guide.databaseObject.detail": "在这里选择需要进行授权的数据库对象，数据源类型层级不支持授权，需要展开到连接层级", "db.auth.dataSource": "数据源配置", "db.auth.modal.title": "手动授权", "db.auth.authorizedObject": "客体授权", "db.auth.resourceList": "资源列表：", "db.auth.noElement": "暂无元素", "db.auth.permModal.batchEditTool": "批量编辑工具权限", "db.auth.permModal.operation": "操作权限", "db.auth.permModal.template": "快速使用权限模板", "db.auth.permModal.name": "权限名", "db.auth.permModal.dataSourceType": "数据源类型", "db.auth.permModal.other": "其他方式控制", "db.auth.permList.disableOrEnable.tip": "确认{{val}}权限吗？", "db.auth.permList.revoke": "回收权限", "db.auth.permList.unrevoke": "不回收权限", "db.auth.permList.delete.tip": "确认删除此权限等级吗？", "db.auth.permList.name": "权限等级名", "db.auth.permList.dept": "关联用户/资源", "db.auth.permList.creator": "创建人", "db.auth.permList.editor": "最后修改人", "db.auth.permList.history": "修订历史", "db.auth.permList.alert": "自定义操作权限集合，在授权时可将自定义权限授权给用户", "db.auth.permList.tab.customPermLevel": "自定义权限等级", "db.auth.permList.tab.highRiskApply": "高危申请详情", "db.auth.permList.permName": "权限名：", "db.auth.permList.desc": "备注：", "db.auth.permList.dataSourceType": "数据源类型：", "db.auth.permList.resltionUser.delete.tip": "确认删除吗？", "db.auth.permList.resltionUser.objects": "关联资源", "db.auth.permList.resltionUser.title": "关联用户", "db.auth.permList.high.operation": "高危类型", "db.auth.permList.high.object": "高危对象", "db.auth.permList.high.address": "生效用户", "db.auth.connectSetting.guide.title": "添加用户", "db.auth.connectSetting.guide.content": "点击添加用户，选择需要进行授权的用户以及相应权限", "db.auth.connectSetting": "连接设置", "db.auth.connectSetting.settingSuccess": "设置成功", "db.auth.connectSetting.alert": "当前授权范围对连接下的所有对象生效", "db.auth.connectSetting.tab.basic": "基础配置", "db.auth.connectSetting.tab.users": "用户授权", "db.auth.connectSetting.tab.basic.env": "测试环境", "db.auth.connectSetting.tab.basic.hideMode": "隐藏模式", "db.auth.connectSetting.tab.basic.doubleCheck.otp": "OTP复核", "db.auth.connectSetting.tab.basic.doubleCheck.none": "关闭复核", "db.auth.connectSetting.tab.basic.doubleCheck.sms": "短信复核", "db.auth.connectSetting.tab.basic.strictMode": "宽松拦截模式", "db.auth.connectSetting.tab.basic.doubleCheck": "同步复核方式", "db.auth.connectSetting.tab.basic.smsUserType": "同步复核审批人", "db.auth.connectSetting.tab.basic.smsUserType.plac": "请选择同步复核审批人", "db.auth.connectSetting.tab.users.delete.tip": "确认移除用户吗？", "db.auth.connectSetting.tab.users.reset.tip": "确认重置", "db.auth.connectSetting.tab.users.reset.content": "此操作将同步当前层级下所有资源的{{val}}与当前层级保持一致,请谨慎操作", "db.auth.connectSetting.tab.users.permissionLevel": "权限等级", "db.auth.connectSetting.tab.users.allPerm": "全部权限", "db.auth.connectSetting.tab.users.templateValid": "此用户在此层级下存在其他权限设置", "db.auth.connectSetting.tab.users.addOperateAuth": "附加操作权限", "db.auth.connectSetting.tab.users.tool": "工具权限", "db.auth.connectSetting.tab.users.role": "附加操作权限", "db.auth.connectSetting.tab.users.beginTime": "权限等级生效时间", "db.auth.connectSetting.tab.users.permissionInfo": "权限详情", "db.auth.connectSetting.tab.users.roleType": "连接角色", "db.auth.connectSetting.tab.users.batchDelete.tip": "此用户设置于上层级，无法移除，请从上层开始移除，或者进行禁用/更换权限等级操作", "db.auth.connectSetting.tab.users.search": "请输入用户名/id/权限等级名称进行检索", "db.auth.connectSetting.tab.users.reset.defaultTitle": "是否重置所选用户权限等级", "db.auth.connectSetting.tab.users.reset.defaultContent": "所选用户在此层级下存在其他权限设置，重置将清空下层级所选用户的权限设置", "db.auth.connectSetting.tab.users.selectUser": "请先选择用户", "db.auth.connectSetting.tab.users.selectPerm1": "请先选择权限", "db.auth.connectSetting.tab.users.selectPerm2": "请选择权限", "db.auth.connectSetting.tab.users.selectTime": "请选择有效时间", "db.auth.connectSetting.tab.users.selectTimePeriod": "请选择时间段", "db.auth.connectSetting.tab.users.selectedUser": "已选择{{val}}个用户", "db.auth.connectSetting.tab.users.effectiveTime": "生效时间", "db.auth.connectSetting.tab.users.effectiveTime.plac": "请选择生效时间", "db.auth.connectSetting.tab.users.timeRange": "时间段", "db.auth.connectSetting.tab.users.timeRange.hint": "起始时间和结束时间都不能为空", "db.auth.connectSetting.tab.users.timeRange.hint2": "请重新选择时间，结束时间必须大于当前时间", "db.auth.connectSetting.tab.users.timeRange.hint3": "起始时间必须小于结束时间", "db.auth.connectSetting.tab.users.batchOperation.tip": "请先选择权限模版", "db.auth.connectSetting.tab.users.batchOperation": "批量设置操作权限", "db.auth.connectSetting.tab.users.fineGrit": "操作权限", "db.auth.connectSetting.tab.users.deleteSelectedPerm": "确认批量删除选中权限？", "db.auth.connectSetting.tab.users.timeRange.plac": "请完善时间", "db.auth.connectSetting.tab.users.operations.plac": "请选择附加操作权限", "db.auth.connectSetting.tab.users.batchEffectTime": "批量编辑生效时间", "db.auth.connectSetting.tab.users.fineGrit.title": "附加操作权限管理", "db.auth.connectSetting.tab.users.addPerm": "添加权限", "db.auth.connectSetting.tab.users.permissionName": "权限", "db.auth.connectSetting.tab.users.sourceType": "权限来源", "db.auth.connectSetting.tab.users.authorizer": "授权人", "db.auth.connectSetting.tab.users.resource": "资源", "db.auth.connectSetting.tab.users.validTime": "有效时间", "db.auth.connectSetting.tab.users.authorizationTime": "授权时间", "db.auth.connectSetting.tab.users.deleteTool.tip": "确定删除？", "db.auth.connectSetting.tab.users.toolManage": "工具权限管理", "db.auth.connectSetting.tab.users.operations.tip": "请选择工具权限", "db.auth.connectSetting.tab.users.effectiveTime.label": "生效时间：", "db.auth.connectSetting.object.tableName": "表名", "db.auth.connectSetting.object.authUpdateMsg": "权限更新", "db.auth.connectSetting.object.belongCategory": "所属分类", "db.auth.connectSetting.object.belongGrade": "所属等级", "db.auth.connectSetting.object.authUserCnt": "权限用户", "db.auth.connectSetting.object.synonymType": "同义词类型", "db.auth.connectSetting.object.object": "对象", "db.auth.connectSetting.object.search": "搜索{{val}}", "db.auth.connectSetting.object.alert1": "当前授权对象可对", "db.auth.connectSetting.object.alert2": "单个", "db.auth.connectSetting.object.alert3": "授权，如需批量操作可点击", "db.auth.connectSetting.object.pageNum": "当前处于第{{num}}页", "db.auth.connectSetting.object.pageTo": "跳转到", "db.auth.connectSetting.object.pageNum.hint": "请输入正整数", "db.auth.connectSetting.object.securitySetting": "安全设置", "db.auth.connectSetting.object.delete.tip": "此用户设置于上层级，无法移除，请从上层开始移除，或者进行禁用/更换权限等级操作", "db.auth.connectSetting.object.updatePerm.success": "用户权限更新成功", "db.auth.connectSetting.object.updatePerm.error": "用户权限更新失败：{{val}}", "db.auth.connectSetting.object.validTime.success": "权限生效时间更新成功", "db.auth.connectSetting.object.validTime.error": "权限生效时间更新失败：{{val}}", "db.auth.connectSetting.object.remove.tip": "确认移除吗？", "db.auth.connectSetting.object.addUser": "新增用户", "db.auth.connectSetting.object.authSearch.plac": "请输入用户名/id/操作权限进行检索", "db.auth.dataProtection.title": "数据保护管理", "db.auth.dataProtection.loadingMore": "加载更多...", "db.auth.dataProtection.resourceList": "资源列表：", "db.auth.dataProtection.desensImport": "脱敏配置导入", "db.auth.dataProtection.guide.title": "资源列表", "db.auth.dataProtection.guide.content1": "在这里展开资源列表到具体表层级并选中需要进行字段脱敏的表", "db.auth.dataProtection.guide.content2": "在这里选择需要进行脱敏扫描任务创建的数据库对象层级", "db.auth.dataProtection.tab.desens": "脱敏设置", "db.auth.dataProtection.tab.filter.tip": "需要进行行过滤设置请展开到具体表上即可设置", "db.auth.dataProtection.desensImport.search": "搜索文件名", "db.auth.dataProtection.dataMasking.datasourceType": "数据库类型", "db.auth.dataProtection.dataMasking.connectionName": "连接名", "db.auth.dataProtection.dataMasking.databaseName": "数据库名", "db.auth.dataProtection.dataMasking.fileName": "导入文件名", "db.auth.dataProtection.dataMasking.completedTime": "完成时间", "db.auth.dataProtection.dataMasking.countOfSucceed": "导入行数", "db.auth.dataProtection.dataMasking.countOfFailed": "失败行数", "db.auth.dataProtection.dataMasking.countOfFailed.detail": "失败详情", "systemMonitor.containerMonitor.title": "容器监控", "systemMonitor.machineMonitor.title": "主机监控", "msg.subTitle": "个人消息", "db.batchAuth.title": "批量授权", "db.subjectAuth.title": "主体授权", "tool.toolConfig.title": "工具配置", "tool.controlRecord.title": "管控记录", "db.auth.toolPerm.tip": "只能选择一项权限", "db.auth.toolPerm.selectDatabaseInstance": "请选择数据库实例", "db.auth.toolPerm.flowPrivilege": "流程提权", "db.auth.toolPerm.autoPrivilege": "自动授权", "db.auth.permDetail.delete.tip": "确认删除？", "db.auth.permDetail.loading": "加载中", "db.auth.permDetail.sourceType": "权限来源", "db.conn.connMember.address": "地址", "db.conn.connMember.post": "端口", "db.conn.connMember.hint": "请输入{{title}}", "db.conn.selectedUser": "已选择用户", "db.conn.object.remove.tip": "此用户设置于上层级，无法移除，请从上层开始移除，或者进行禁用/更换权限等级操作", "db.conn.effectTive.forever": "永久", "db.conn.updateFile.success": "{{name}}上传成功", "db.conn.updateSingleFile.tip": "只允许上传一个文件", "db.conn.updateSingleFile.type": "请上传{{accept}}格式的文件", "db.conn.deleteFile": "删除文件", "msg.viewDetail": "查看通知详情", "db.connection.approver.editConnection": "编辑审批人组连接", "db.conn.host": "数据库主机连接", "db.conn.assetCatalog": "资产目录", "db.conn.tableAssetsCatalog": "表资产目录", "db.conn.fieldAssetsCatalog": "字段资产目录", "db.conn.inputPlaceholder": "请输入表名称/备注", "db.conn.selectPlaceholder1": "所属数据库", "db.conn.selectPlaceholder2": "所属schema", "db.conn.tableName": "表名称", "db.conn.tableRemark": "表备注", "db.conn.tableFieldCnt": "列数量", "db.conn.tableDataCnt": "数据条数", "db.conn.tableSchema": "所属schema", "db.conn.tableDatabase": "所属数据库", "db.conn.operate": "操作", "db.conn.fileName": "文件名称", "db.conn.fileName.hint": "请输入以.csv结尾的文件名", "db.conn.operate.fieldInfo": "列信息", "db.conn.operate.dataDetail": "数据详情", "db.conn.operate.tableRemarkEdit": "表备注编辑", "db.conn.fieldAsset.inputPlaceholder": "请输入列名/列备注/表名/表备注", "db.conn.fieldAsset.fieldName": "列名", "db.conn.fieldAsset.fieldRemark": "列备注", "db.conn.fieldAsset.fieldType": "字段类型", "db.conn.fieldAsset.fieldLength": "字段长度", "db.conn.fieldAsset.fieldPrecision": "字段精度", "db.conn.fieldAsset.belongingTable": "所属表", "db.conn.fieldAsset.tableRemark": "表备注", "db.conn.fieldAsset.belongingSchema": "所属schema", "db.conn.fieldAsset.belongingDatabase": "所属数据库", "db.conn.method": "连接方式", "db.conn.keyAuth.tip": "使用密钥方式连接，每次连接时不需要输入密码，但需要预先配置", "db.conn.viewConfig.tip": "点击查看如何配置", "db.conn.pwdCon": "密码连接", "db.conn.keyCon": "密钥连接", "db.conn.port": "端口号", "db.conn.port.plac": "请输入端口号", "db.conn.gotIt": "知道了", "db.conn.ensureSshConfig": "配置SSH服务器: 编辑服务器上的/etc/ssh/sshd_config文件,确保以下设置已启用", "db.conn.ensureSshConfig.detail": "PubkeyAuthentication yes; AuthorizedKeysFile .ssh/authorized_keys", "db.conn.downloadUploadKey": "下载公钥，并上传到要连接的服务器上", "db.conn.keyAuth.uploadKeyTip": "下载公钥文件，手动将公钥文件内容复制到目标数据库主机服务器上的 ~/.ssh/authorized_keys 文件进行追加。请确保数据库服务器开启了密钥认证方式并重启ssh服务", "db.conn.keyCon.completeTip": "完成上述操作后，点击密钥连接后输入用户名及端口即可连接", "db.conn.downloadPublicKey": "下载公钥", "db.connection.conn.space.hint": "输入内容不能包含空格", "db.conn.autoAuth.remove.tip": "自动授权权限不支持在此处移除/禁用/重置", "db.conn.autoAuth.permLevel.tip": "仅展示来源为手动授权的权限等级，其他来源的权限等级可通过权限详情查看", "db.conn.autoAuth.permOperation.tip": "仅展示来源为手动授权的操作权限，其他来源的操作权限可通过权限详情查看", "db.conn.autoAuth.search.placeholder": "请输入权限名称/资源/授权人进行检索", "db.conn.autoAuth.permDetail.edit.tip": "权限来源于自动授权，不支持编辑生效时间/不支持删除", "db.conn.flowAuth.permDetail.edit.tip": "权限来源于流程提权，不支持编辑生效时间", "db.conn.columnInfo.title": "列信息", "db.conn.columnInfo.fieldName": "字段名", "db.conn.columnInfo.fieldRemark": "字段备注", "db.conn.columnInfo.fieldType": "字段类型", "db.conn.columnInfo.fieldLength": "字段长度", "db.conn.columnInfo.fieldPrecision": "字段精度", "db.conn.columnInfo.fieldExample": "示例数据", "db.conn.columnInfo.errorInfo": "获取列信息失败", "db.conn.tableEdit.errorInfo": "修改表备注失败", "db.conn.tableEdit.successInfo": "修改表备注成功", "db.conn.tableRemark.requireMsg": "请输入表备注", "db.conn.export.success.mgs": "导出成功", "db.conn.export.error.mgs": "导出失败", "db.conn.explore.msg": "暂无数据，请先执行数据探查操作"}