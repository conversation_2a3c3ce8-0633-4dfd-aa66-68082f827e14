{"classGrading.title": "Data Classification and Grading", "classGrading.tab.template.title": "Template Management", "classGrading.tab.tag.title": "Tag Management", "classGrading.tab.task.title": "Classification and Grading Tasks", "classGrading.tab.result.title": "Grading Results", "classGrading.tab.template.tab.builtIn": "Built-in Templates", "classGrading.tab.template.tab.new": "New Template", "classGrading.tab.template.action.addClass": "Add Classification", "classGrading.tab.template.action.addTemplate": "Add Template", "classGrading.tab.template.action.gradeConfig": "Grading Configuration", "classGrading.tab.template.action.importTemplate": "Import Template", "classGrading.tab.task.timeCycle.MANUAL": "Manual", "classGrading.tab.task.timeCycle.DAY": "Daily", "classGrading.tab.task.timeCycle.TWO_DAY": "Every Two Days", "classGrading.tab.task.timeCycle.THREE_DAY": "Every Three Days", "classGrading.tab.task.timeCycle.WEEK": "Weekly", "classGrading.tab.task.timeCycle.TWO_WEEK": "Every Two Weeks", "classGrading.tab.task.timeCycle.MONTH": "Every Month", "classGrading.tab.task.status.notStarted": "Not Started", "classGrading.tab.task.status.pending": "Pending", "classGrading.tab.task.status.success": "Success", "classGrading.tab.task.status.failed": "Failed", "classGrading.tab.result.res.all": "All", "classGrading.tab.result.res.grade": "Graded", "classGrading.tab.result.res.unGrade": "Ungraded", "classGrading.tab.result.tab.column": "Column Results", "classGrading.tab.result.tab.table": "Table Results", "classGrading.tab.result.column.columnName": "Column Name", "classGrading.tab.result.column.columnDesc": "Column Description", "classGrading.tab.result.column.tag": "Associated Tag", "classGrading.tab.result.column.class": "Associated Classification", "classGrading.tab.result.column.model": "Model Classification", "classGrading.tab.result.column.grade": "Associated Grading", "classGrading.tab.result.column.table": "Associated Table", "classGrading.tab.result.column.tableDesc": "Table Description", "classGrading.tab.result.column.schema": "Associated Schema", "classGrading.tab.result.column.database": "Associated Database", "classGrading.tab.result.column.connection": "Associated Connection", "classGrading.tab.result.column.connectionType": "Connection Type", "classGrading.tab.result.column.address": "Connection Address", "classGrading.tab.result.column.classTask": "Associated Classification Task", "classGrading.tab.result.column.searchPlac": "Please enter column name/column description/table name/table description/associated classification", "classGrading.tab.result.column.searchTask.plac": "Please select a classification task", "classGrading.tab.result.column.view": "Grading Details", "classGrading.tab.result.column.tableName": "Table Name", "classGrading.tab.result.column.radio": "Classification Proportion", "classGrading.tab.result.column.count": "Grading Count", "classGrading.tab.result.column.level": "Associated Level", "classGrading.tab.result.column.isOverThreshold": "Exceeds Threshold", "classGrading.tab.result.column.isManual": "Is Manually Graded", "classGrading.tab.result.column.manual": "Set as Manual Grading", "classGrading.tab.result.columnInfo": "Column Information", "classGrading.tab.result.column.className": "Classification Name", "classGrading.tab.result.column.manualClassName": "Manual Classification", "classGrading.tab.result.column.classCount": "Classification Count", "classGrading.tab.result.column.totalColumnCount": "Total Column Count", "classGrading.tab.result.column.classPercentage": "Grading Percentage", "classGrading.tab.result.column.tableName1": "Table Name", "classGrading.tab.result.tableSearch.plac": "Please enter table name/description/associated classification", "classGrading.tab.task.column.taskName": "Task Name", "classGrading.tab.task.column.dataSource": "Associated Data Source", "classGrading.tab.task.column.executeType": "Execution Type", "classGrading.tab.task.column.executeStrategy": "Execution Strategy", "classGrading.tab.task.column.scheduleCycle": "Schedule Cycle", "classGrading.tab.task.column.industryType": "Associated Industry Type", "classGrading.tab.task.column.template": "Classification Template", "classGrading.tab.task.column.executeTask": "Execute Task", "classGrading.tab.task.column.recognitionResult": "Recognition Result", "classGrading.tab.task.editTask": "Edit Classification Task", "classGrading.tab.task.addTask": "Add Classification Task", "classGrading.tab.task.taskName.plac": "Please enter task name", "classGrading.tab.task.dataSource.plac": "Please select associated data source", "classGrading.tab.task.industryType.plac": "Please select associated industry type", "classGrading.tab.task.template.plac": "Please select classification template", "classGrading.tab.task.executeType.value1": "Engine Recognition", "classGrading.tab.task.executeType.value2": "Large Model Recognition", "classGrading.tab.task.executeStrategy.value1": "Full", "classGrading.tab.task.executeStrategy.value2": "Incremental", "classGrading.tab.task.scheduleCycle.plac": "Please select schedule cycle", "classGrading.tab.task.executeTime": "Execution Time", "classGrading.tab.task.executeTime.plac": "Please select execution time", "classGrading.export.fileName.label": "File Name", "classGrading.export.fileName.plac": "Please enter file name", "classGrading.export.fileName.hint": "Please enter a file name ending with .csv", "classGrading.tab.tag.addIndustryType": "Add Industry Type", "classGrading.tab.tag.searchPlac": "Please enter tag name to search", "classGrading.tab.tag.column.tagName": "Tag Name", "classGrading.tab.tag.column.industryType": "Industry Type", "classGrading.tab.tag.column.totalMatchWeight": "Total Match Weight", "classGrading.tab.tag.column.fieldName": "Field Column Name", "classGrading.tab.tag.column.fieldRemark": "Field Column Description", "classGrading.tab.tag.column.tableName": "Table Name", "classGrading.tab.tag.column.tableRemark": "Table Description", "classGrading.tab.tag.column.dataLength": "Data Length", "classGrading.tab.tag.column.keyword": "Contains Keyword", "classGrading.tab.tag.column.notKeyword": "Does Not Contain Keyword", "classGrading.tab.tag.column.regular": "Regular Expression", "classGrading.tab.tag.column.sensitive": "Personal Sensitive Information", "classGrading.tab.tag.column.industry": "Industry Sensitive Information", "classGrading.tab.tag.column.importantData": "Important Data", "classGrading.tab.tag.column.coreData": "Core Data", "classGrading.tab.tag.column.modifyTime": "Last Modified Time", "classGrading.tab.tag.column.tagStatus": "Tag Status", "classGrading.tab.tag.addTag": "Add Tag", "classGrading.tab.tag.name.label": "Name", "classGrading.tab.tag.name.plac": "Please enter name", "classGrading.tab.tag.desc.label": "Description", "classGrading.tab.tag.desc.plac": "Please enter description", "classGrading.tab.tag.createTag.label": "Create Data Source Tag", "classGrading.tab.tag.createTag.plac": "Please select data source tag", "classGrading.tab.tag.dataSource.label": "Data Source", "classGrading.tab.tag.dataSource.plac": "Please select data source", "classGrading.tab.tag.industry.label": "Industry Information", "classGrading.tab.tag.industry.plac": "Please select industry information", "classGrading.tab.tag.file.label": "Select File", "classGrading.tab.tag.file.btnText": "File Upload", "classGrading.tab.tag.file.plac": "Please upload file", "classGrading.tab.tag.identSample.label": "Identification Sample", "classGrading.tab.tag.indentTemplate.label": "Identification Template", "classGrading.tab.tag.tagName": "Tag", "classGrading.tab.tag.gradeConfig": "Level Configuration", "classGrading.tab.tag.level": "Tag Level", "classGrading.tab.tag.category": "Tag Category", "classGrading.tab.template.delete.tip": "Current node has child nodes and cannot be deleted", "classGrading.tab.template.delete.tip2": "Current classification has associated tags and cannot be deleted", "classGrading.tab.template.editClass": "Edit Classification", "classGrading.tab.template.addClass": "Add Classification", "classGrading.tab.template.className.label": "Classification Name", "classGrading.tab.template.className.plac": "Please enter classification name", "classGrading.tab.template.editTemplate": "Edit Template", "classGrading.tab.template.addTemplate": "Add Template", "classGrading.tab.template.templateName.label": "Template Name", "classGrading.tab.template.templateName.plac": "Please enter template name", "classGrading.tab.template.tagType.label": "Tag Type", "classGrading.tab.template.tagType.plac": "Please select tag type", "classGrading.tab.template.relatedTemplate.label": "Associated Grading Template", "classGrading.tab.template.relatedTemplate.plac": "Please select associated grading template", "classGrading.tab.template.level.label": "Level", "classGrading.tab.template.level.plac": "Please select level", "classGrading.tab.template.connMethod.label": "Association Method", "classGrading.tab.template.connMethod.plac": "Please select association method", "classGrading.tab.template.asscoTag.label": "Associated Tag Import Sample", "classGrading.tab.template.asscoDatasouce.label": "Associated Data Source Import Sample", "classGrading.tab.template.dataSource.label": "Associated Data Source", "classGrading.tab.template.dataSource.plac": "Please select the associated data source", "classGrading.tab.template.gradeName.label": "Grading Name", "classGrading.tab.template.gradeName.plac": "Please enter the grading name", "classGrading.tab.template.gradeName.defaultText": "Default template name + grading", "classGrading.tab.template.example.relatedTag": "Related Tag Import Sample.xlsx", "classGrading.tab.template.example.relatedDataSource": "Related Data Source Import Sample.xlsx", "classGrading.tab.result.data_length.plac": "e.g., equal to 8, greater than 8, less than 8", "classGrading.tab.result.data_length.label": "Data Type", "classGrading.tab.result.keyword": "Contains Keyword", "classGrading.tab.result.keyword.plac": "Zhang San||Li Si, separated by '||'", "classGrading.tab.result.colname.plac": "Column1||Column2, separated by '||'", "classGrading.tab.result.table_name.plac": "Table1||Table2, separated by '||'", "classGrading.tab.result.data_type.string": "String", "classGrading.tab.result.data_type.number": "Number", "classGrading.tab.task.status.delete": "Associated Task Deleted", "classGrading.tab.task.status.completed": "Completed", "classGrading.tab.task.status.in_progress": "In Progress", "classGrading.tab.task.identify_type.local": "Identify Local Database", "classGrading.tab.task.identify_type.bussiness": "Identify Business Database", "classGrading.tab.tag.edit.title": "Edit Tag", "classGrading.tab.tag.label_name.label": "Label Name", "classGrading.tab.tag.label_name.plac": "Please enter the label name", "classGrading.tab.tag.threshold.plac": "Please enter the total matching weight", "classGrading.tab.tag.field.plac": "Please enter {{val}}", "classGrading.tab.tag.threshold.label": "Recognition Weight", "classGrading.tab.tag.threshold.hint": "Recognition weight must be between 0 and 0.9!", "classGrading.tab.tag.type.plac": "Please select the industry type", "classGrading.tab.tag.indentTemplate.fileName": "Identification Template.xlsx", "classGrading.tab.tag.identSample.fileName": "Identification Sample.xlsx", "classGrading.tab.task.identify_type.label": "Identification Method", "classGrading.tab.task.identify_type.plac": "Please select the identification method", "classGrading.tab.tag.threshold.hint2": "The total matching weight must be between 0 and 0.9!"}