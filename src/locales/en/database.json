{"db.connection.title": "Database Management", "db.connection.connection": "Connection Management", "db.connection.delete.archiveConnection.tip": "Are you sure you want to delete this archived connection?", "db.connection.test.connection": "Test Connection", "db.connection.keepOpenDialog": "Keep the dialog open", "db.connection.available": "Connection Available", "db.connection.update.approverGroup": "The associated connection [{{boundArr}}] already has an approver group. Do you want to set this change as final?", "db.connection.noDataSource": "No data source types available to create", "db.connection.guide": {"okText": "Create Now", "cancelText": "Ignore", "content": "Create a new connection and manage it on the platform."}, "db.connection.create": "New Connection", "db.connection.noPerm": "Your current role is [{{roleNameList}}], and you do not have permission to operate on [Connection Management].", "db.connection.list": "Connection List:", "db.connection.sdt": {"group": "Group View", "instance": "Instance View", "approver": "Approver Group View"}, "db.connection.approverGroup.delete.signal": "Are you sure you want to delete {{groupName}}?", "db.connection.approverGroup.new": "New Approver Group", "db.connection.noPerm2": "Only DBA or Senior User role can create connections", "db.connection.sdt.deleteGroup.tip": "Are you sure you want to disband the group?", "db.connection.sdt.moveoutConn.tip.title": "Are you sure you want to move out the connection?", "db.connection.sdt.moveoutConn.tip.content1": "Once enabled, moving out the connection will clear all previous related settings, including permission settings, data protection settings, etc.", "db.connection.sdt.moveoutConn.tip.extra2": "Do you want to clear the related settings on the connection?", "db.connection.sdt.addConn": "Add Connection", "db.connection.sdt.addGroup": "New Group", "db.connection.sdt.moveoutConn": "Move Out Connection", "db.connection.sdt.copyConn": "Copy Connection", "db.connection.sdt.editConn": "Edit Connection", "db.connection.sdt.testConn": "Test Connection", "db.connection.sdt.deleteConn": "Delete Connection", "db.connection.sdt.setManager": "Set Connection Manager", "db.connection.sdt.editName": "Edit Name", "db.connection.resetPerm.title": "Should the related settings of the group be inherited", "db.connection.resetPerm.content": "If you choose to inherit, the newly added connections in the group will inherit the previous settings of the group, including permission settings, data protection settings, etc.", "db.connection.sdt.relieve": "Relieve", "db.connection.sdt.clearConnSetting.title": "Do you want to clear the related settings on the connection", "db.connection.sdt.clearConnSetting.content": "If you choose to clear, the connections moved out of the group will clear all previous related settings, including permission settings, data protection settings, etc.", "db.connection.group.addConn.success": "Connection added successfully", "db.connection.group.moveoutConn.success": "Connection moved out successfully", "db.connection.group.groupName.empty": "Group name cannot be empty!", "db.connection.group.modifyGroupName": "Modify Group Name", "db.connection.group.groupName": "Group Name", "db.connection.group.groupName.hint": "Please enter the group name", "db.connection.group.connectionIds": "Select Connections", "db.connection.group.connectionIds.hint": "Please select connections", "db.connection.group.inheritToken": "Should the related settings of the group be inherited", "db.connection.group.connectionIds.extra": "If enabled, the newly added connections in the group will inherit the previous settings of the group, including permission settings, data protection settings, etc.", "db.connection.group.moveoutIsClear": "Should the related settings on the connection be cleared", "db.connection.group.moveoutIsClear.extra": "If enabled, the connections moved out of the group will clear all previous related settings, including permission settings, data protection settings, etc.", "db.connection.conn.create": "New", "db.connection.conn.monitorSwitch": "Monitoring Switch", "db.connection.conn.connSuccess": "{{val}} connection successful", "db.connection.conn.connAvailable": "Connection Available", "db.connection.conn.connFail": "Connection Failed", "db.connection.conn.openAllSchema": "Should All Vusiness Databases Be Opened", "db.connection.conn.filterResources": "Select System Libraries Defined In The Filter Configuration File", "db.connection.conn.isAdminWithAllPerms": "Obtain All Access Permissions For This Connection", "db.connection.conn.userName": "Username", "db.connection.conn.userName.plac": "Please enter username", "db.connection.conn.password": "Password", "db.connection.conn.password.plac": "Please enter password", "db.connection.conn.principal.plac": "Please enter Principal", "db.connection.conn.keytab.plac": "Please upload Keytab file (.keytab)", "db.connection.conn.krb5_config": "Please upload Krb5 config file (.conf)", "db.connection.conn.zk_principal.plac": "Please enter ZK Principal", "db.connection.conn.edit.title": "{{conn}} Connection", "db.connection.conn.panel1": "Fill In Connection Information", "db.connection.conn.panel2": "Connection Configuration", "db.connection.conn.pool.setting.success": "Connection pool configuration successful", "db.connection.conn.panel2.pageRows.success": "Pagination row count setting successful during query", "db.connection.conn.panel2.workConfig.success": "Transaction settings successful", "db.connection.conn.panel2.rowNum.hint": "Pagination row count must be a positive integer", "db.connection.conn.panel2.rowNum.hint2": "Pagination row count range is 100~2000", "db.connection.conn.panel2.workConfig": "Transaction Settings", "db.connection.conn.panel2.workConfig.auto": "Allow auto-commit", "db.connection.conn.panel2.workConfig.manual": "Disallow auto-commit", "db.connection.conn.panel2.pool": "Connection Pool Configuration", "db.connection.conn.panel2.otherSetting": "Other Configurations", "db.connection.conn.panel2.pageRows": "Pagination Row Count", "db.connection.conn.panel2.pageRows.plac": "Please enter pagination row count during query", "db.connection.conn.formItem.plac": "Please select {{label}}", "db.connection.conn.formItem.required": "{{label}} cannot be empty", "db.connection.conn.formItem.plac2": "Please select or add {{label}}", "db.connection.conn.implicitCommitTimeout.hint": "Please enter a reasonable positive integer", "db.connection.conn.implicitCommitTimeout.hint2": "Please enter an integer greater than or equal to 60 and less than or equal to 86400", "db.connection.conn.sqlBackupStatus": {"success": "Backup configuration successful", "label": "Data Backup", "extra": "Only supports data backup for delete, update, drop, truncate operations"}, "db.connection.conn.sqlFlashBackSetting": {"label": "SQL Flashback Functionality", "extra": "The flashback function must be enabled with data backup"}, "db.connection.conn.sqlBackupLocation": "Backup Location", "db.connection.conn.sqlBackUpTime": "Backup Time", "db.connection.conn.backupConfig": "Backup Configuration", "db.connection.conn.sqlBackUpTime.hint": "Please select backup time", "db.connection.conn.secretKey": "Change Secret Key", "db.connection.conn.secretKey.tip": "Cannot be empty", "db.connection.conn.approver.updateSuccess": "Change successful", "db.connection.conn.approver.connManager": "Connection Manager", "db.connection.conn.approve": "Approver Group", "db.connection.conn.approve.plac": "Please select approver group", "db.connection.conn.approve.new": "New Approver Group", "db.connection.conn.batchAddConn": "Batch add connections", "db.connection.conn.connManager.success": "Admin updated successfully", "db.connection.conn.connManager.org": "Organizational Structure", "db.connection.approver.new": "New Approver Group", "db.connection.approver.edit": "Edit Approver Group", "db.connection.approver.editUser": "Edit Approver Group Users", "db.connection.approver.groupName.plac": "Please enter the approver group name", "db.connection.approver.groupName": "Approver Group Name", "db.connection.approver.users": "Approver Group Users", "db.connection.approver.users.plac": "Please select approver group users", "db.connection.approver.connections": "Associated Connections", "db.connection.approver.connections.plac": "Please select associated connections", "db.connection.approver.removeUser.tip": "Are you sure you want to remove this user?", "db.connection.approver.connectionType": "Database Type", "db.connection.approver.connectionName": "Connection Name", "db.connection.approver.connectionUrl": "Connection URL", "db.connection.approver.ruleTemplateName": "Template Name", "db.connection.approver.SID": "SID/Service Name", "db.connection.approver.userName": "Username", "db.connection.approver.manager": "Connection Administrator", "db.connection.approver.creator": "Creator", "db.connection.approver.createDateTime": "Creation Date and Time", "db.connection.approver.delete.tip": "Are you sure you want to remove?", "db.connection.tabs.archive.tip": "Archived connections cannot be viewed", "db.connection.tabs.archive": "Archived", "db.connection.tabs.overview": "Connection Overview", "db.connection.tabs.overview.guide.content1": "Please ensure that the corresponding resources are enabled; otherwise, authorization management and other operations cannot be performed.", "db.connection.tabs.overview.guide.setting": "Go to settings now", "db.connection.tabs.overview.resourceManagement": "Resource Management", "db.connection.tabs.overview.poolManagement": "Connection Pool Management", "db.connection.tabs.overview.connectionMode.replicaSet": "Replica Set Mode", "db.connection.tabs.overview.userAndConnCount": "User Count/Connection Count", "db.connection.tabs.overview.sqlAndExecuteCount": "Today's SQL/Total Executions", "db.connection.tabs.overview.logicalBase": "Logical Database", "db.connection.tabs.overview.tableCount": "Table Count", "db.connection.tabs.overview.dataSourceType": "Data Source Type", "db.connection.tabs.resourceManagement.note.hint": "Maximum 50 characters!", "db.connection.tabs.resourceManagement.note.success": "Note updated successfully", "db.connection.tabs.resourceManagement.note.updateTitle": "Note Update", "db.connection.tabs.resourceManagement.dbName": "Resource Name", "db.connection.tabs.resourceManagement.dbName.plac": "Please enter the resource name", "db.connection.tabs.resourceManagement.admin": "Administrator", "db.connection.tabs.resourceManagement.enableStatus": "Status", "db.connection.tabs.resourceManagement.note": "Note", "db.connection.tabs.resourceManagement.closeTip": "Are you sure you want to close this resource?", "db.connection.tabs.poolManagement.totalConnectTime": "Total Connection Time", "db.connection.tabs.poolManagement.totalConnectTime.tip": "Duration of the JDBC connection to the database", "db.connection.tabs.poolManagement.totalRunTime": "Total Runtime", "db.connection.tabs.poolManagement.totalRunTime.tip": "Duration of user-occupied connections", "db.connection.tabs.poolManagement.option": "Status Management", "db.connection.tabs.poolManagement.allSuspend.tip": "Are you sure you want to terminate all?", "db.connection.tabs.poolManagement.allSuspend": "Terminate All", "db.connection.tabs.poolManagement.explainPlan": "Execution Plan", "db.connection.tabs.poolManagement.sql.identify": "【{{index}}】", "db.connection.archiveConnection.tip": "Are you sure you want to archive this connection?", "db.connection.archiveConnection.success": "Archiving successful", "db.connection.unfile.tip": "Are you sure you want to unarchive this connection?", "db.connection.unfile.success": "Unarchiving successful", "db.connection.undoProblem.success": "Problem retraction successful", "db.connection.batchDelete.tip": "Are you sure you want to delete in bulk?", "db.connection.batchDelete.success": "Bulk deletion successful", "db.connection.batchTest": "Batch Test", "db.connection.batchArchive": "Batch Archive", "db.connection.batchUndoProblem": "<PERSON><PERSON> Undo <PERSON>", "db.connection.batchUnfile": "Batch Unarchive", "db.connection.undoProblem": "Undo Problem", "db.connection.unfile": "Unarchive", "db.connection.copyConn.tip": "Only DBA or Senior User role can copy connections", "db.connection.groupName": "Instance Name", "db.connection.createUser": "Created by:", "db.connection.nodeName": "Instance Address:", "db.connection.serviceName": "SID/Service Name:", "db.connection.search": "Please enter the content to search", "db.connection.allConnectionsTab": "Active Connections", "db.connection.problemConnectionsTab": "Problem Connections", "db.connection.archiveConnectionTab": "Archived Connections", "db.auth.guide.databaseObject": "Database Object", "db.auth.guide.databaseObject.detail": "Select the database objects that need authorization here. Data source type hierarchy does not support authorization; it needs to be expanded to the connection level.", "db.auth.dataSource": "Data Source Configuration", "db.auth.modal.title": "Manual Authorization", "db.auth.authorizedObject": "Object Authorization", "db.auth.resourceList": "Resource List:", "db.auth.noElement": "No elements available", "db.auth.permModal.batchEditTool": "Batch Edit Tool Permission", "db.auth.permModal.operation": "Operation Permission", "db.auth.permModal.template": "Quick Use Permission Template", "db.auth.permModal.name": "Permission Name", "db.auth.permModal.dataSourceType": "Data Source Type", "db.auth.permModal.other": "Other Control Methods", "db.auth.permList.disableOrEnable.tip": "Are you sure you want to {{val}} the permission?", "db.auth.permList.revoke": "Revoke Permission", "db.auth.permList.unrevoke": "Do Not Revoke Permission", "db.auth.permList.delete.tip": "Are you sure you want to delete this permission level?", "db.auth.permList.name": "Permission Level Name", "db.auth.permList.dept": "Associated Users/Resources", "db.auth.permList.creator": "Creator", "db.auth.permList.editor": "Last Modified By", "db.auth.permList.history": "Revision History", "db.auth.permList.alert": "Custom operation permission set, which can be granted to users during authorization.", "db.auth.permList.tab.customPermLevel": "Custom Permission Level", "db.auth.permList.tab.highRiskApply": "High-Risk Application Details", "db.auth.permList.permName": "Permission Name:", "db.auth.permList.desc": "Remarks:", "db.auth.permList.dataSourceType": "Data Source Type:", "db.auth.permList.resltionUser.delete.tip": "Are you sure you want to delete?", "db.auth.permList.resltionUser.objects": "Associated Resources", "db.auth.permList.resltionUser.title": "Associated Users", "db.auth.permList.high.operation": "High-Risk Type", "db.auth.permList.high.object": "High-Risk Object", "db.auth.permList.high.address": "Effective User", "db.auth.connectSetting.guide.title": "Add User", "db.auth.connectSetting.guide.content": "Click to add a user, select the user that needs authorization and the corresponding permissions.", "db.auth.connectSetting": "Connection Settings", "db.auth.connectSetting.settingSuccess": "Settings Successful", "db.auth.connectSetting.alert": "The current authorization scope applies to all objects under the connection.", "db.auth.connectSetting.tab.basic": "Basic Configuration", "db.auth.connectSetting.tab.users": "User Authorization", "db.auth.connectSetting.tab.basic.env": "Test Environment", "db.auth.connectSetting.tab.basic.hideMode": "Hidden Mode", "db.auth.connectSetting.tab.basic.doubleCheck.otp": "OTP Review", "db.auth.connectSetting.tab.basic.doubleCheck.none": "Disable Review", "db.auth.connectSetting.tab.basic.doubleCheck.sms": "SMS Review", "db.auth.connectSetting.tab.basic.strictMode": "Loose Interception Mode", "db.auth.connectSetting.tab.basic.doubleCheck": "Synchronous Review Method", "db.auth.connectSetting.tab.basic.smsUserType": "Synchronous Review Approver", "db.auth.connectSetting.tab.basic.smsUserType.plac": "Please select a synchronous review approver", "db.auth.connectSetting.tab.users.delete.tip": "Are you sure you want to remove the user?", "db.auth.connectSetting.tab.users.reset.tip": "Confirm Reset", "db.auth.connectSetting.tab.users.reset.content": "This operation will synchronize all resources under the current level's {{val}} to be consistent with the current level. Please proceed with caution.", "db.auth.connectSetting.tab.users.permissionLevel": "Permission Level", "db.auth.connectSetting.tab.users.allPerm": "All Permissions", "db.auth.connectSetting.tab.users.templateValid": "This user has other permission settings at this level.", "db.auth.connectSetting.tab.users.addOperateAuth": "Additional Operation Permissions", "db.auth.connectSetting.tab.users.tool": "Tool Permissions", "db.auth.connectSetting.tab.users.role": "Additional Operation Permissions", "db.auth.connectSetting.tab.users.beginTime": "Effective Time", "db.auth.connectSetting.tab.users.permissionInfo": "Permission Details", "db.auth.connectSetting.tab.users.roleType": "Connection Role", "db.auth.connectSetting.tab.users.batchDelete.tip": "This user is set at a higher level and cannot be removed. Please remove from the higher level or disable/change permission level.", "db.auth.connectSetting.tab.users.search": "Please enter username/id/permission level name for search", "db.auth.connectSetting.tab.users.reset.defaultTitle": "Do you want to reset the selected user's permission level?", "db.auth.connectSetting.tab.users.reset.defaultContent": "The selected user has other permission settings at this level. Resetting will clear the permission settings for the selected user at the lower level.", "db.auth.connectSetting.tab.users.selectUser": "Please select a user first.", "db.auth.connectSetting.tab.users.selectPerm1": "Please select a permission first.", "db.auth.connectSetting.tab.users.selectPerm2": "Please select a permission.", "db.auth.connectSetting.tab.users.selectTime": "Please select an effective time.", "db.auth.connectSetting.tab.users.selectTimePeriod": "Please select a time period.", "db.auth.connectSetting.tab.users.selectedUser": "{{val}} users selected.", "db.auth.connectSetting.tab.users.effectiveTime.plac": "Please select an effective time.", "db.auth.connectSetting.tab.users.timeRange": "Time Range", "db.auth.connectSetting.tab.users.timeRange.hint": "Both start time and end time cannot be empty.", "db.auth.connectSetting.tab.users.timeRange.hint2": "Please reselect the time; the end time must be greater than the current time.", "db.auth.connectSetting.tab.users.timeRange.hint3": "The start time must be less than the end time.", "db.auth.connectSetting.tab.users.effectiveTime": "Effective time", "db.auth.connectSetting.tab.users.batchOperation.tip": "Please select a permission template first.", "db.auth.connectSetting.tab.users.batchOperation": "Batch Set Operation Permissions", "db.auth.connectSetting.tab.users.fineGrit": "Operation Permissions", "db.auth.connectSetting.tab.users.deleteSelectedPerm": "Are you sure you want to batch delete the selected permissions?", "db.auth.connectSetting.tab.users.timeRange.plac": "Please complete the time.", "db.auth.connectSetting.tab.users.operations.plac": "Please select additional operation permissions.", "db.auth.connectSetting.tab.users.batchEffectTime": "Batch Edit Effective Time", "db.auth.connectSetting.tab.users.fineGrit.title": "Additional Operation Permissions Management", "db.auth.connectSetting.tab.users.addPerm": "Add Permission", "db.auth.connectSetting.tab.users.permissionName": "Permission", "db.auth.connectSetting.tab.users.sourceType": "Permission Source", "db.auth.connectSetting.tab.users.authorizer": "Authorizer", "db.auth.connectSetting.tab.users.resource": "Resource", "db.auth.connectSetting.tab.users.validTime": "Valid Time", "db.auth.connectSetting.tab.users.authorizationTime": "Authorization Time", "db.auth.connectSetting.tab.users.deleteTool.tip": "Are you sure you want to delete?", "db.auth.connectSetting.tab.users.toolManage": "Tool Permission Management", "db.auth.connectSetting.tab.users.operations.tip": "Please select tool permissions.", "db.auth.connectSetting.tab.users.effectiveTime.label": "Effective Time:", "db.auth.connectSetting.object.tableName": "Table Name", "db.auth.connectSetting.object.authUpdateMsg": "Permission Update", "db.auth.connectSetting.object.belongCategory": "Belonging Category", "db.auth.connectSetting.object.belongGrade": "Belonging Grade", "db.auth.connectSetting.object.authUserCnt": "Authorized Users", "db.auth.connectSetting.object.synonymType": "Synonym Type", "db.auth.connectSetting.object.object": "Object", "db.auth.connectSetting.object.search": "Search {{val}}", "db.auth.connectSetting.object.alert1": "The current authorized object can", "db.auth.connectSetting.object.alert2": "single", "db.auth.connectSetting.object.alert3": "authorization. For batch operations, click", "db.auth.connectSetting.object.pageNum": "Currently on page {{num}}", "db.auth.connectSetting.object.pageTo": "Jump to", "db.auth.connectSetting.object.pageNum.hint": "Please enter a positive integer.", "db.auth.connectSetting.object.securitySetting": "Security Settings", "db.auth.connectSetting.object.delete.tip": "This user is set at a higher level, cannot be removed. Please remove from the upper level or perform disable/change permission level operations.", "db.auth.connectSetting.object.updatePerm.success": "User permissions updated successfully", "db.auth.connectSetting.object.updatePerm.error": "User permissions update failed: {{val}}", "db.auth.connectSetting.object.validTime.success": "Permission validity time updated successfully", "db.auth.connectSetting.object.validTime.error": "Permission validity time update failed: {{val}}", "db.auth.connectSetting.object.remove.tip": "Confirm removal?", "db.auth.connectSetting.object.addUser": "Add User", "db.auth.connectSetting.object.authSearch.plac": "Enter username/id/permission to search", "db.auth.dataProtection.title": "Data Protection Management", "db.auth.dataProtection.loadingMore": "Loading more...", "db.auth.dataProtection.resourceList": "Resource List:", "dataProtection.noPerm": "You currently have no permission on [Data Protection] as [roleNameList]", "db.auth.dataProtection.desensImport": "Data Masking", "db.auth.dataProtection.guide.title": "Resource List", "db.auth.dataProtection.guide.content1": "Expand the resource list to the specific table level and select the tables you need to desensitize.", "db.auth.dataProtection.guide.content2": "Select the database object level here to create desensitization scan tasks.", "db.auth.dataProtection.tab.desens": "Desensitization Settings", "db.auth.dataProtection.tab.filter.tip": "To set row filters, expand to the specific table level and configure there.", "db.auth.dataProtection.desensImport.search": "Search for file name", "db.auth.dataProtection.dataMasking.datasourceType": "Database Type", "db.auth.dataProtection.dataMasking.connectionName": "Connection Name", "db.auth.dataProtection.dataMasking.databaseName": "Database Name", "db.auth.dataProtection.dataMasking.fileName": "Import File Name", "db.auth.dataProtection.dataMasking.completedTime": "Completed Time", "db.auth.dataProtection.dataMasking.countOfSucceed": "Number of Imported Rows", "db.auth.dataProtection.dataMasking.countOfFailed": "Number of Failed Rows", "db.auth.dataProtection.dataMasking.countOfFailed.detail": "Failure Details", "systemMonitor.containerMonitor.title": "Container Monitoring", "systemMonitor.machineMonitor.title": "Machine Monitoring", "msg.subTitle": "Personal Messages", "db.batchAuth.title": "Batch Authorization", "db.subjectAuth.title": "Subject Authorization", "tool.toolConfig.title": "Tool Configuration", "tool.controlRecord.title": "Control Records", "db.auth.toolPerm.tip": "You can only select one permission", "db.auth.toolPerm.selectDatabaseInstance": "Please select a database instance", "db.auth.toolPerm.flowPrivilege": "Flow Privilege", "db.auth.toolPerm.autoPrivilege": "Auto Authorization", "db.auth.permDetail.delete.tip": "Confirm deletion?", "db.auth.permDetail.loading": "Loading", "db.auth.permDetail.sourceType": "Authorization Source", "db.conn.connMember.address": "Address", "db.conn.connMember.post": "Port", "db.conn.connMember.hint": "Please enter {{title}}", "db.conn.updateFile.success": "{{name}} uploaded successfully", "db.conn.updateSingleFile.tip": "Only one file is allowed to be uploaded", "db.conn.updateSingleFile.type": "Please upload a file in {{accept}} format", "db.conn.deleteFile": "Delete File", "msg.viewDetail": "View Notification Details", "db.connection.approver.editConnection": "Edit Approver Group Connection", "db.conn.host": "Database Host Connection", "db.conn.assetCatalog": "Asset Catalog", "db.conn.tableAssetsCatalog": "Table of Asset Catalog", "db.conn.fieldAssetsCatalog": "Field of Asset Catalog", "db.conn.fileName": "File Name", "db.conn.fileName.hint": "Please input file name with .csv suffix", "db.conn.inputPlaceholder": "Please input table name/comment", "db.conn.selectPlaceholder1": "Belonging Database", "db.conn.selectPlaceholder2": "Belonging <PERSON><PERSON><PERSON>", "db.conn.tableName": "Table Name", "db.conn.tableRemark": "Table Remark", "db.conn.tableFieldCnt": "Field Count", "db.conn.tableDataCnt": "Data Count", "db.conn.tableSchema": "<PERSON><PERSON><PERSON>", "db.conn.tableDatabase": "Database", "db.conn.operate": "Operate", "db.conn.operate.fieldInfo": "Field Info", "db.conn.operate.dataDetail": "Data Detail", "db.conn.operate.tableRemarkEdit": "Table Remark Edit", "db.conn.fieldAsset.inputPlaceholder": "Please input column name/column remark/table name/table remark", "db.conn.fieldAsset.fieldName": "Column Name", "db.conn.fieldAsset.fieldRemark": "Column Remark", "db.conn.fieldAsset.fieldType": "Field Type", "db.conn.fieldAsset.fieldLength": "Field Length", "db.conn.fieldAsset.fieldPrecision": "Field Precision", "db.conn.fieldAsset.belongingTable": "Belonging Table", "db.conn.fieldAsset.tableRemark": "Table Remark", "db.conn.fieldAsset.belongingSchema": "Belonging <PERSON><PERSON><PERSON>", "db.conn.fieldAsset.belongingDatabase": "Belonging Database", "db.conn.method": "Connection Method", "db.conn.keyAuth.tip": "Connecting v zia key authentication does not require entering a password each time, but requires prior configuration", "db.conn.viewConfig.tip": "Click to view how to configure", "db.conn.pwdCon": "Password Connection", "db.conn.keyCon": "Key Connection", "db.conn.port": "Port", "db.conn.port.plac": "Please enter port number", "db.conn.gotIt": "Got It", "db.conn.ensureSshConfig": "Configure SSH server: Edit the /etc/ssh/sshd_config file on the server and ensure that the following settings are enabled.", "db.conn.ensureSshConfig.detail": "PubkeyAuthentication yes; AuthorizedKeysFile .ssh/authorized_keys", "db.conn.downloadUploadKey": "Download public key and upload to the target server", "db.conn.keyAuth.uploadKeyTip": "Download the public key file and manually copy its contents to the `~/.ssh/authorized_keys` file on the target database host server for appending. Please ensure that the database server has enabled key-based authentication and restart the SSH service.", "db.conn.keyCon.completeTip": "After completing the above steps, click Key Connection and enter the username and port to connect", "db.conn.downloadPublicKey": "Download Public Key", "db.connection.conn.space.hint": "The input content cannot contain Spaces", "db.conn.autoAuth.remove.tip": "Auto-authorization permissions do not support removal/disabling/resetting", "db.conn.autoAuth.permLevel.tip": "Only display permissions Level from manual authorization, other authorization permissions Level can be viewed in the permission details", "db.conn.autoAuth.permOperation.tip": "Only display operation permissions from manual authorization, other operation permissions can be viewed in the permission details", "db.conn.autoAuth.search.placeholder": "Please enter permission name/resource/authorized person to search", "db.conn.autoAuth.permDetail.edit.tip": "The permission is from auto-authorization, editing the effective time or deleting is not supported", "db.conn.flowAuth.permDetail.edit.tip": "The permission is from flow privilege, editing the effective time is not supported", "db.conn.columnInfo.title": "Column Information", "db.conn.columnInfo.fieldName": "Field Name", "db.conn.columnInfo.fieldRemark": "Field Remark", "db.conn.columnInfo.fieldType": "Field Type", "db.conn.columnInfo.fieldLength": "Field Length", "db.conn.columnInfo.fieldPrecision": "Field Precision", "db.conn.columnInfo.fieldExample": "Example Data", "db.conn.columnInfo.errorInfo": "Failed to get column information", "db.conn.tableEdit.errorInfo": "Table remark edited unsuccessfully", "db.conn.tableEdit.successInfo": "Table remark edited successfully", "db.conn.tableRemark.requireMsg": "Please input table remark", "db.conn.export.success.mgs": "Export success", "db.conn.export.error.mgs": "Export failed", "db.conn.explore.msg": "No data available, please execute data profiling first"}