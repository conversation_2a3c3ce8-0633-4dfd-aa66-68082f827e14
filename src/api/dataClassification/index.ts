import { fetchGet, fetchPost, fetchPostFormData } from '../customFetch';

const classUrlPrefix = '/user/classification/forward';

export type ClassBuiltTempItem =  {
  tp_id: number;
  label_number: number;
  tp_name: string;
  subclass_count: number;
}

//模板管理-内置模板
export const getClassBuiltTempAPI = (params: any): Promise<ClassBuiltTempItem[]>  =>{
  return fetchPost(`${classUrlPrefix}/api/built_temp`, params);
}
//模板管理-新增模板
export const getClassNewTemplateAPI = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/template/manage`, params);
}

//模板管理-内置模板-查询标签
export const getClassBuiltTempTagList = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/query_tags_by_temp`, params);
}

//模板管理-模板-绑定标签
export const getClassBuiltTempBindTag = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/bind_labels`, params);
}

//模板管理-内置模板-新增分类
export const getClassAddCategory = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/add_category`, params);
}

//模板管理-新增分类（新接口）
export const getClassAddClassification = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/add_classification`, params);
}

//模板管理-编辑分类（新接口）
export const getClassEditClassification = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/edit_classification`, params);
}

//模板管理-删除分类（新接口）
export const getClassDelClassification = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/del_classification`, params);
}

//模板管理-绑定分级配置（新接口）
export const getClassBindCls = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/bind_cls`, params);
}
//模板管理-内置模板-删除分类
export const getClassDelCategory = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/del_actegory`, params);
}

//模板管理-内置模板-删除分类
export const getClassEditCategory = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/edit_category`, params);
}

//模板管理-内置模板-绑定分级
export const getClassBindClsLevel = (params: any): Promise<ClassBuiltTempItem[]> => {
  return fetchPost(`${classUrlPrefix}/api/cls_bind`, params);
}


export type IClassIndustryItem = {
  name: string;
  type: number;
}
export type IClassIndustryParams  = {
  action: string;
  key: string;
  name?: string;
  type?: number;
}
export const  ClassStorageAPI = (params: IClassIndustryParams): Promise<IClassIndustryItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/storage`, params);
}

export type IClassTagParams = {
  action: string;
  label_name?: string;
  limit?: string;
  label_id?: number;
  type?: number;
  id?: number;
}

export type IClassTagItem = {

}
//模板管理 -行业类型

export const  ClassTagIndustryListAPI = (params: {limit: number}): Promise<IClassTagItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/industry_type_list`, params);
}

export const  ClassTagDeleteIndustryAPI = (params: {id: number}): Promise<IClassTagItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/industry_del`, params);
}

export const  ClassClassTagAPI = (params: IClassTagParams): Promise<IClassTagItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/classify_lev`, params);
}
//模板管理 -行业类型编辑
export interface IClassTagEditIndustryParams {
  name: string;
  details: string;
  id?: number;
  select_id: string;
  src_id: string
}
export const  ClassTagEditIndustry = (params: IClassTagEditIndustryParams): Promise<IClassTagItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/industry_edit`, params);
}
//模板管理 -行业类型- 新增
export const  ClassTagAddIndustry = (params: IClassTagEditIndustryParams): Promise<IClassTagItem[]> =>{
  return fetchPost(`${classUrlPrefix}/api/industry_add`, params);
}

interface IClassIndustryDatasourceParams {
  key: string
}
// 添加 行业 数据源
export const  getClassDatasourceAPI = (params: IClassIndustryDatasourceParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/query_storage`, params);
}

//标签管理 -导入
export const  ClassImportTagAPI = (params: any): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/db/classify_lev`, params);
}
//标签管理 -导入 -下载模板
export const  ClassTagDownloadExampleAPI = async (params: any): Promise<void> =>{
  const requestUrl = `${classUrlPrefix}/api/download_example?filename=${params.filename}`;
  const response = await fetch(requestUrl, {
    method: 'GET',
    credentials: 'include'
  });
  
  if (response.ok) {
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = params.filename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } else {
    throw new Error('下载失败');
  }
}

//查询标签列表（支持多层级动态加载）
export interface IQueryTagListParams {
  action: string;
  label_name?: string;
  type?: string;
  label_id?: string;
  limit?: string;
}
export const QueryTagListAPI = (params: IQueryTagListParams): Promise<any> => {
  return fetchPost(`${classUrlPrefix}/api/classify_lev`, params);
}

//分类分级 -列表
export const  ClassTagTaskAPI = (): Promise<any[]> =>{
  return fetchGet(`${classUrlPrefix}/api/query_task_list`);
}

//分类分级 -删除
export const  ClassTagDeleteTaskAPI = (params: any): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/del_task`, params);
}

//分类分级 -新增
export const  ClassTaskAddTaskAPI = (params: any): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/add_task`, params);
}


//分类分级 -新增
export const  ClassTaskEditTaskAPI = (params: any): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/edit_task`, params);
}
//分类分级 -新增-关联数据源
export const  ClassTaskDatasourceAPI = (): Promise<any[]> =>{
  return fetchGet(`${classUrlPrefix}/api/query_datasource`);
}


export const  ClassTaskIndustryTypeAPI = (params: IClassIndustryDatasourceParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/query_storage`, params);
}

export interface IClassTaskExecuteTaskParams {
  classifys_id: number;
  id: number;
  recon_id: number
}
//分类分级 -执行任务
export const  ClassTaskExecuteTaskAPI = (params: IClassTaskExecuteTaskParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/exe_tasks`, params);
}

//结果-列结果
export const  ClassResColumnAPI = (params: IClassTaskExecuteTaskParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/query_column_grade`, params);
}
// //结果-表结果
export const  ClassResTableAPI = (params: IClassTaskExecuteTaskParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/query_table_grade`, params);
}

export interface IClassResTableColumnDetailParams {
  table_name?: string;
  src_id?: number;
  schema_name?: string;
  classify_id?: string;
  limit?: number | string;
}
//结果-表结果 -列详情
export const  ClassResTableColumnDetailAPI = (params: IClassTaskExecuteTaskParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/query_table_columns`, params);
}

export interface IClassResTableGradeDetailParams {
  table_name?: string;
  src_id?: number;
  schema_name?: string;
  database_name?: string;
  limit?: number | string;
}
//结果-表结果 -定级详情
export const  ClassResTableGradeDetailAPI = (params: IClassResTableGradeDetailParams): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/query_grade_details`, params);
}

//结果-表结果 -定级详情-人工定级
export const  ClassResTableSetManualGradeAPI = (params: {classify_id: number} ): Promise<any[]> =>{
  return fetchPost(`${classUrlPrefix}/api/set_manual_grade`, params);
}

// 查询关联方式 - 查询存储数据
export interface IQueryStorageParams {
  key: string;
}
export const QueryStorageAPI = (params: IQueryStorageParams): Promise<any> => {
  return fetchPost(`${classUrlPrefix}/api/query_storage`, { key: params.key });
}

// 查找标签类型列表
export const QueryLabelTypeAPI = (): Promise<any> => {
  return fetchPost(`${classUrlPrefix}/api/query_storage`, { key: 'dd:dams_label_type' });
}

// 查询关联分级模板列表
export const QueryTemplateSelectLvAPI = (): Promise<any> => {
  return fetchPost(`${classUrlPrefix}/api/query_storage`, { key: 'dd:data_template_selectlv' });
}

// 查询关联数据源列表
export const QueryDataSourceAPI = (): Promise<any> => {
  return fetchPost(`${classUrlPrefix}/api/query_storage`, { key: 'dd:data_dams_dbms' });
}

// 文件上传接口
export interface IFileUploadParams {
  jUploaderFile: File;
  filetype: string;
}
export const ClassFileUploadAPI = (params: IFileUploadParams): Promise<any> => {
  console.log(params, 'params')
  return fetchPostFormData(`${classUrlPrefix}/api/cls_putfile`, {
    jUploaderFile: params.jUploaderFile,
    filetype: params.filetype
  });
}

// 模板导入接口
export interface ITemplateImportParams {
  [key: string]: any;
}
export const ClassTemplateImportAPI = (params: ITemplateImportParams): Promise<any> => {
  return fetchPost(`${classUrlPrefix}/api/template/manage`, params);
}

export interface IClassResultExportParams {
  filename: string;
  ids: number[];
}
//分级结果 - 列结果-导出接口
export const ClassResultColExportAPI = (params: IClassResultExportParams): Promise<any> => {
  return fetchPost(`${classUrlPrefix}/api/col_export`, params);
}

//分级结果 - 列结果 查询类别
export const ClassResultClassAPI = (): Promise<any> => {
  return fetchGet(`${classUrlPrefix}/api/query_classify`);
}

//分级结果 - 表结果-导出接口
export const ClassResultTableExportAPI = (params: IClassResultExportParams): Promise<any> => {
  return fetchPost(`${classUrlPrefix}/api/table_export`, params);
}