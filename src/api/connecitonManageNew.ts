
import { fetchDelete, fetchGet, fetchPost, fetchPut } from "./customFetch"
import type { ISearchSdtItem } from './sdt';
import { DataSourceType } from 'src/api';
import { getIsBase64EncodeVal, strToBase64 } from "src/util";



/**
 * 新增组
 */
interface addGroupParams {
  datasource: string,
  groupName: string
  connectionIds?: number[]
}
export const createConnectionGroup = (params: addGroupParams) => {
  return fetchPost(`/user/group/create`, params)
}

/**
 * 修改组名
 */
interface modifyGroupNameParams {
  id: number,
  groupName: string
}
export const modifyConnectionGroupName = (params: modifyGroupNameParams) => {
  return fetchPost(`/user/group/update`, params)
}

/**
 * 删除组
 */
export const deleteGroup = (id: number) => {
  return fetchDelete(`/user/group/${id}`)
}

/**
 * 获取当前用户在不同分组视图节点下的已有权限
 */
interface IGetNodePermissions {
  dataSourceType: string,
  nodeType: string
  nodePathWithType: string
}
export const getNodePermissions = (params: IGetNodePermissions) => {
  return fetchPost(`/user/permission/object/findPermission`, params)
}

/**
 * 查询分组视图
 */
export const queryGroupNodes = (doFilter: any) => {
  return fetchGet(`/user/connections/connection/nodes/${doFilter}`);
}

// 结果集提权查询分组视图
export const queryAllGroupNodes = () => {
  return fetchGet(`/user/connections/connection/allnodes`);
}
export interface IQueryGroupNodesAutomicParams {
  pageNo: number,
  pageSize: number,
  roleName: string,
  isFlow?: boolean
}
export const queryGroupNodes_automic = (params: IQueryGroupNodesAutomicParams) => {
  return fetchPost(`/user/permissionCollection/getPermissionCollection`, params);
}

/**
 * 根据数据源查连接
 */
export const queryDatasourceConnections = (datasource: string) => {
  return fetchGet(`/user/group/datasource/connections/?datasource=${datasource}`);
}

/**
 * 同构复制数据查询连接
 */
export const queryDataDuplicationConnections = (datasource: string) => {
  return fetchGet(`/user/group/datasource/connections/?datasource=${datasource}&hideNoPermission=true`);
}
/**
 * 根据数据源、页码查连接
 */
interface Params {
  datasource: string,
  pageNo: string | number,
  pageSize: string | number,
  likeQueryFlag?: boolean,
  queryKeyWords?: string,
  isProblemConnection?: boolean,  // 查询问题连接
  isArchiveConnection?: boolean,  // 查询归档连接
}

export const queryDatasourceConnectionsByPage = (params: Params) => {
  const {
    datasource,
    pageNo,
    pageSize,
    likeQueryFlag = false,
    queryKeyWords,
    isProblemConnection = false,
    isArchiveConnection = false,
  } = params;
  let urlParams = `datasource=${datasource}&pageNo=${pageNo}&pageSize=${pageSize}&isProblemConnection=${isProblemConnection}&isArchiveConnection=${isArchiveConnection}`;
  if (likeQueryFlag && queryKeyWords && queryKeyWords.length > 0) {
    urlParams += `&likeQueryFlag=${likeQueryFlag}&queryKeyWords=${queryKeyWords}`;
  }
  return fetchGet(`/user/group/datasource/connections/page?${urlParams}`);
};

/**
 * 根据组查询连接
 */
export const queryGroupConnections = (groupId: number, rest?: any) => {
  if (rest?.hasOwnProperty("isProblemConnection") && rest?.hasOwnProperty("isArchiveConnection")) {
    return fetchGet(`/user/group/${groupId}/connections`, { ...rest })
  } else {
    return fetchGet(`/user/group/${groupId}/connections`)
  }
}

/**
 * 查询连接(组能新增的所有连接)
 */
// interface queryConnetionParams {
//   groupId: number
// 	datasource: string
// 	[p:string]: any
// }
export const queryCanAddConnections = (datasource: string) => {
  // const paramsString = Object.keys(params)
  //   .map((key) => key + '=' + params[key])
  //   .join('&')
  return fetchGet(`/user/group/unmap/connections?datasource=${datasource}`)
}

/* 数字字典查询列表 */
export const getProtectSdtSearchList = (params: { query: string }): Promise<ISearchSdtItem[]> =>
  fetchGet(`/dms/fullTextSearch/searchDataProtectSdt`, params)


/**
 * 添加连接
 */
interface addConnectionParams {
  id: number
  connectionIds: number[]
  inheritToken?: boolean;
  dataSourceType?: string
}
export const addConnection = (params: addConnectionParams) => {
  return fetchPost(`/user/group/connection/map/create`, params)
}

/**
 * 移出连接（移除组和连接的关系）
 */
export const removeConnection = (connetionIds: number[], isClear: boolean) => {
  return fetchDelete(`/user/group/connection/remove/${isClear}`, connetionIds)
}

/**
 * 新建连接
 */
interface createParams {
  dataSourceType: any
  connectionId?: number,
  userInputs: {
    dataSourceVersion: string
    connectionName: string
    connectionUrl: string
    connectionPort: number
    userName: string
    password: string
    remark: string
    devModel: boolean
    dataSourceType: string
  }
}
export const createConnection = (params: createParams) => {
  return fetchPost(`/dms/connection/configuration`, params)
}

/**
 * 编辑连接
 */
export const editConnection = (params: createParams) => {
  return fetchPut(`/dms/connection/configuration`, params)
}



/**
 * 测试连接
 */
export const testConnection = (params: createParams) => {
  return fetchPost(`/dms/connection/effectiveness`, params)
}

/**
 * 获取上一次测试连接的连接状态
 */
export const getLastConnectionStatus = (params: { connectionId: number }) => {
  return fetchPost(`/user/connections/testConnectionStatus`, params)
}

/**
 * 删除连接 
 */
interface deleteParams {
  connectionId: number
  connectionType: string
  nodeName: string
  nodePath: string
  nodeType: string
}
export const deleteConnection = (params: deleteParams) => {
  return fetchDelete(`/dms/menu`, params)
}

/**
 * 批量删除连接 
 */
interface deleteParams {
  connectionIds: number
  connectionType: string
  nodeName: string
  nodePath: string
  nodeType: string
}
export const deleteConnectionBatch = (params: deleteParams) => {
  return fetchDelete(`/dms/menu/batch`, params)
}

/**
 * 根据连接查连接下的资源（pdb、schema）--已有
 */
interface getConnectionInfoParams {
  connectionId: number | string
  connectionType: string
  nodeName: string
  nodePath: string
  nodeType: string
  groupId: number
}
export const getConnectionInfo = (params: getConnectionInfoParams) => {
  return fetchPost(`/dms/meta/node`, params)
}

/**
 *  数据源信息查询
 */
export const getdmsConnectionConfig = (
  connectionId: number,
) => {
  return fetchGet(`/dms/connection/form/connection/${connectionId}`)
}

/**
 * 根据连接获取连接池配置信息   --已有
 */
export const getConnectionPoolConfig = (connectionId: number) => {
  return fetchGet(
    `/dms/connection/form/connection_pool_setting/${connectionId}`,
  )
}

/**
 * 根据连接获取连接池默认配置信息
 */
export const getConnectionPoolDefaultConfig = (dataSourceType: string) => {
  return fetchGet(
    `/dms/connection/form/default_pool_setting/${dataSourceType}`,
  )
}

/**
 * 根据连接更新连接池配置信息   --已有
 */
interface updateConnectionPoolParams {
  connectionId: number
  userInputs: {
    maxSize: number
    minSize: number
    borrowMaxWaitMillis: number
    preparePool: boolean
    testOnBorrow: boolean
    removeAbandonedTimeout: number
    autoClosePoolTimeout: number
    implicitCommitTimeout: number
    transactionType: string
  }
}
export const updateConnectionPoolConfig = (params: updateConnectionPoolParams) => {
  return fetchPost(`/dms/connection/form/connection_pool_setting`, params)
}

/**
 * 根据连接获取事务配置信息   --已有
 */
export const getConnectionWorkConfig = (connectionId: number) => {
  return fetchGet(
    `/user/connections/conectionSetting/${connectionId}/allowAutoCommit`,
  )
}

/**
 * 根据连接更新事务配置信息  --已有
 */
interface IUpdateWorkParams {
  variable: string
  variable_value: boolean | string
}
interface IUpdateConnectionWorkParams extends IUpdateWorkParams {
  connectionId: number | string | undefined
}
export const updateConnectionWorkConfig = (params: IUpdateConnectionWorkParams) => {
  return fetchPost(`/user/connections/conectionSetting`, params)
}
interface IUpdateGroupWorkParams extends IUpdateWorkParams {
  groupId: number | string | undefined
}
export const updateGroupWorkConfig = (params: IUpdateGroupWorkParams) => {
  return fetchPost(`/user/connections/group/conectionSetting`, params)
}
interface IUpdateDataSourceWorkParams extends IUpdateWorkParams {
  datasourceType: string
}
export const updateDataSourceWorkConfig = (params: IUpdateDataSourceWorkParams) => {
  return fetchPost(`/user/connections/datasource/conectionSetting`, params)
}


/**
 * 数据备份 可选连接
 */
export const getSqlBackupLocationConnections = (params: {
  datasource: string;
  displayOwnerOnly: boolean
}) => {
  return fetchGet(`/analyze/audit_report/connections`, params)
}

/**
 * 查询链接管理员信息
 */
export const queryConnectionManageInfo = (connectionId: string) => {
  return fetchGet(`/user/group/connection/${connectionId}/manager`)
}

/**
 * 查询可以更改的管理员列表
 */
export const queryManageList = () => {
  return fetchGet(`/user/org/findUserOrgSdtVo/manager`)
}

/**
 * 检查用户是否拥有对应连接下的权限
 *
 * @param params 权限检查参数
 * @returns 返回权限检查结果
 */
interface ICheckUserPermissionParams {
  connectionId: number | string
  userId: string
}
export const checkUserPermission = (params: ICheckUserPermissionParams) => {
  return fetchPost(`/user/permission/sdt/user/check/user/permission`, params)
}

/**
 * 根据连接更新连接管理员信息
 */
interface updateManageParams {
  connectionId: number
  userId: string
}
export const updateManageInfo = (params: updateManageParams) => {
  return fetchPost(`/user/group/connection/manager`, params)
}

/**
 * 查询数据源连接池连接数量
 */
export const queryConnectionCount = (connectionId: number) => {
  return fetchGet(`/dms/tx/connection/${connectionId}/pool/count`)
}

/**
 * 查询今日SQL，总SQL   SQLCount
 */
export const querySQLCount = (connectionName: string,) => {
  return fetchGet(`/user/group/${connectionName}/sql/count`)
}

/**
 * 授权的用户数量 
 */
export const queryUserCount = (connectionId: number) => {
  return fetchGet(`/userCount/${connectionId}`)
}

/**
 * 库表统计
 */
export const queryDBTableCount = (connectionId: number) => {
  return fetchGet(`/user/group/${connectionId}/database/table/count`);
};


interface IConnectionForLikeQuery {
  connectionIds: string[],
  likeQueryFlag?: boolean,  // 是否模糊搜索
  queryKeyWords?: string
}
/**
 * 批量连接查询+模糊搜索 （根据connectionId查询连接信息）
 */
export const queryConnectionForLikeQuery = (params: IConnectionForLikeQuery) => {
  return fetchPost(`/user/connections/connectionForLikeQuery`, params)
}


/**
 * 组管理员获取
 */
export const queryGroupManage = (groupId: string) => {
  return fetchGet(`/user/group/${groupId}/manager`)
}

/**
 * 组管理员修改
 */
interface groupParamsI {
  userId: string,
  groupId: string
}
export const updateGroupManage = (params: groupParamsI) => {
  return fetchPost(`/user/group/${params?.groupId}/manager`, params)
}


/**
 * 获取连接池管理列表信息
 */
interface ConTableInfoParamsI {
  connectionId: string;
  dataSourceType: string;
  [p: string]: any
}
export const queryConnectionTableInfo = (params: ConTableInfoParamsI) => {
  return fetchGet(
    `/dms/tx/connection/get_connection_pool_status_summary/${params?.dataSourceType}/${params?.connectionId}`
  );
};

/**
 * 终止连接 
 */
interface StopParamsI {
  connectionId: number;
  connectionObjectId: string;
}
export const stopExecute = (params: StopParamsI) => {
  return fetchPost(`/dms/tx/connection/closeConnection`, params);
};

/**
 * 执行计划查询
 */
interface planParamsI {
  dataSourceType: string
  connectionId: number
  databaseName: string
  statements: string
}
export const explainPlan = (params: planParamsI) => {
  if (getIsBase64EncodeVal()) {
    params.statements = strToBase64(params?.statements)
  }
  return fetchPost(`/dms/statement/explain`, params);
};

//连接池 全部中止
export const closeAllConnction = (params: {
  connectionId: number
}) => {
  return fetchPost(`/dms/tx/connection/close/connection/all`, params);
};

/**
 * 判断连接是否被归档
 */
export const connectionIsArchive = (connectionId: string | number) => {
  return fetchGet(
    `/user/connections/isArchiveConnection?connectionId=${connectionId}`
  );
};


/**
 * 连接-归档
 */
export const archiveConnection = (params: number[]) => {
  return fetchPost(`/user/connections/archiveConnection`, params);
};

/**
 * 连接-撤销归档
 */
export const unarchiveConnection = (params: number[]) => {
  return fetchPost(`/user/connections/unarchiveConnection`, params);
};

/**
 * 连接-撤销问题
 */
export const removeFailed = (params: number[]) => {
  return fetchPost(`/user/connections/removeFailedCountConnectionIds`, params);
};

//审批人组start

export interface IAddApproverGroupParams {
  groupName: string;
  connectionIds: number[];
  userIds: string[];
}
//新建审批人组
export const addApproverGroup = (params: IAddApproverGroupParams) => {
  return fetchPost(`/api/flow/flow-assignee-group/add-assignee-group`, params);
};

export interface IApproverGroupItem {
  id: number;
  groupName: string;
  isInternal: boolean;
  createUserId: string;
  createdAt: string;
  updatedAt: string;
}
//审批人组列表
export const getApproverGroupList = (params: { groupName?: string }): Promise<IApproverGroupItem[]> => {
  return fetchPost(`/api/flow/flow-assignee-group/assignee-groups`, params);
};

export interface IApproverGroupConnectionItem {
  connectionId: number;
  dataSourceName: DataSourceType;
  connectionName: string;
}

export interface IApproverGroupUserItem {
  groupId: number;
  userId: DataSourceType;
  userName: string;
}
export interface IApproverGroupDetailItem {
  groupId: number;
  connectionList: IApproverGroupConnectionItem[];
  groupName: string;
  userList: IApproverGroupUserItem[];
}
export const getApproverGroupDetail = (groupId: number): Promise<IApproverGroupItem[]> => {
  return fetchGet(`/api/flow/flow-assignee-group/group-details/${groupId}`);
};

//更改名称
export const updateApproverGroupName = (params: { groupName: string, groupId: number }): Promise<any> => {
  return fetchPost(`/api/flow/flow-assignee-group/update-name`, params);
};


export const deleteApproverGroup = (groupId: number): Promise<any> => {
  return fetchDelete(`/api/flow/flow-assignee-group/delete-group/${groupId}`);
};

//检查是否已绑定组
export const checkApproverGroupBingStatus = (params: { connectionIds: number[], groupId?: number }): Promise<any> => {
  return fetchPost(`/api/flow/flow-assignee-group/check-bind-status`, params);
};
//绑定连接
export const bindApproverGroupConn = (params: { connectionIds: number[], groupId: number }): Promise<any> => {
  return fetchPost(`/api/flow/flow-assignee-group/bind-conn`, params);
};

export const connbindApproverGroup = (params: { connectionIds: number[], groupId?: number }): Promise<any> => {
  return fetchPost(`/api/flow/flow-assignee-group/conn-bind-group`, params);
};


//连接解绑
export const unbindApproverGroupConn = (params: { connectionIds: number[], groupId: number }): Promise<any> => {
  return fetchPost(`/api/flow/flow-assignee-group/unbind-conn`, params);
};

//审批人组绑定用户
export const bindApproverGroupUser = (params: { userIds: string[], groupId: number }): Promise<any> => {
  return fetchPost(`/api/flow/flow-assignee-group/bind-user`, params);
};

//用户解绑,最后一个无法解除绑定
export const unbindApproverGroupUser = (params: { userIds: string[], groupId: number }): Promise<any> => {
  return fetchPost(`/api/flow/flow-assignee-group/unbind-user`, params);
};

export interface IApproverGroupUserParams {
  pageSize: number;
  pageNum: number;
  groupId: number;
}
//查询审批人组绑定的用户信息列表
export const getApproverGroupUserList = (params: IApproverGroupUserParams): Promise<any> => {
  return fetchGet(`/user/users/groupId`, params);
};

export interface IApproverGroupConnParams {
  pageSize: number;
  pageNo: number;
  groupId: number;
}
//查询审批人组绑定的用户信息列表
export const getApproverGroupConnList = (params: IApproverGroupConnParams): Promise<any> => {
  return fetchGet(`/user/group/flow-group/connections`, params);
};

//查询当前工单可以指定的审批人列表
export const queryPointApprover = (flowInstanceId: number): Promise<any> => {
  return fetchGet(`/api/flow/flow-assignee-group/query-assignees/${flowInstanceId}`);
};

//查询连接绑定审批人组信息
export const queryConnApprover = (connectionId: number): Promise<any> => {
  return fetchGet(`/api/flow/flow-assignee-group/query-group-info/${connectionId}`);
};

//编辑审批人组信息
export const updateApproverGroupDetail = (params: { groupId: number } & IAddApproverGroupParams): Promise<any> => {
  return fetchPost(`/api/flow/flow-assignee-group/update-group`, params);
};
//审批人组end

/**
 * zich
 */
export interface SearchParams {
    /**
     * 目录类型
     */
    catalog_type: string;
    /**
     * 连接
     */
    connect?: string;
    /**
     * database名
     */
    database?: string;
    /**
     * 数量
     */
    limit?: string;
    /**
     * 查询类型
     */
    query_type: string;
    /**
     * schema
     */
    schema_name?: string;
    /**
     * 表备注
     */
    table_comment?: string;
    /**
     * 表名称
     */
    table_name?: string;
    [property: string]: any;
}


// 获取表/字段资产目录，查询列信息，查询列信息示例数据，查询数据详情，修改表备注，查询表备注
export const getAssetsDataSource = (params: SearchParams): Promise<any> => {
  return fetchPost(`/user/classification/forward/api/assets`, params);
};

// 数据探查
export const exploreData = (id:string): Promise<any> => {
  return fetchGet(`/user/classification/forward/api/explore_datasource?id=${id}`);
}

// 判断系统是否开启了分类分级功能
export const checkClassificationGrade = (): Promise<any> => {
  return fetchGet(`/user/classification/isEnabled`);
}

// 获取支持分级分类的数据源
export const getSupportClassificationGrade = (): Promise<any> => {
  return fetchGet(`/user/classification/getSupportedDataSources`);
}

// 批量导出
export const batchExport = (params: { fileName: string, ids:string }): Promise<any> => {
  return fetchPost(`/user/classification/forward/api/assets_field_export`, params);
}

// 导出时下载文件
export const downloadFile = (filename: string): Promise<any> => {
  return fetchGet(`/user/classification/forward/api/workspace/${filename}`);
}

// 数据源是否执行过数据探查操作
export const checkDataProfiling = (id:string): Promise<any> => {
  return fetchGet(`/user/classification/forward/api/checkTaskStatus/${id}`);
}

// 查询字段分类
export const getColumnType = (): Promise<any> => {
  return fetchGet(`/user/classification/forward/api/query_classify`);
}

// 批量设置非脱敏字段
export const setNonDesensitizeField = (params: {
  nodePath: string;
  datasourceType: string;
  columnType: string;
  ruleName: string;
}[]): Promise<any> => {
  return fetchPost(`/desens/resource/update/batch`, params);
}
