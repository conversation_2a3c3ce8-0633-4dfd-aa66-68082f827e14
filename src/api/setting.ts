import { fetchDelete, fetchGet, fetchPost, fetchPostFormData, fetchPut } from "./customFetch";
import i18n from "i18next";

/** 获取企业名称 */
export const getCompanyName = () => {
  return fetchGet(`/user/sys/company`);
};

/** 修改企业名称 */
export const updateCompanyName = (company: string) => {
  return fetchPut(`/user/sys/company/${company}`);
};

/** 获取邮箱设置 */
export const getEmailConfig = () => {
  return fetchGet(`/user/sys/email`);
};

export interface EmailConfigEntity {
  authCode: string;
  emailAddress: string;
  host: string;
  port: number;
  tls: boolean;
}

/** 修改邮箱设置 */
export const updateEmailConfig = (params: Partial<EmailConfigEntity>) => {
  return fetchPut(`/user/sys/email`, params);
};

/** 测试邮箱设置 */
export const testEmailConfig = (params: Partial<EmailConfigEntity>) => {
  return fetchPost(`/user/sys/email/test`, params);
};

// 获取双因素认证项
export const getTwoFactor = () => {
  return fetchGet(`/user/sys/twoFactor`);
};

// 修改双因素认证项
export const postTwoFactor = (type: string) => {
  return fetchPost(`/user/sys/setTwoFactor?twoFactorEnum=${type}`);
};

//获取失效时间项
export const getExpireTimeByMin = (): Promise<number> => {
  return fetchGet(`/user/session/timeout`);
};

//修改失效时间项
export const postOvertime = (int: number) => {
  return fetchPost(`/user/session/timeout?timeout=${int} `);
};

export const getCopy = () => {
  return fetchGet(`/user/sys/copy`);
};

export const postCopy = (flag: boolean) => {
  return fetchPost(`/user/sys/copy/${flag}`);
};

export const getWatermark = () => {
  return fetchGet(`/user/sys/watermark`);
};

export const postWatermark = (flag: boolean) => {
  return fetchPost(`/user/sys/watermark/${flag}`);
};

export const getTabMax = () => {
  return fetchGet(`/user/sys/tab/max`);
};

export const postTabMax = (count: number) => {
  return fetchPost(`/user/sys/tab/max/${count}`);
};

export const getFileMax = () => {
  return fetchGet(`/user/sys/file/max`);
};

export const postFileMax = (count: number) => {
  return fetchPost(`/user/sys/file/max/${count}`);
};

/* 初始化菜单权限列表 */
export const getInitMenuPerms_api = () => {
  return fetchGet(`/user/permission/permission/menu`);
};

/* 编辑初始化菜单权限 */
export const postInitMenuPerms_api = (data: string[]) => {
  return fetchPost(`/user/permission/update/init/menu`, data);
};

/** 编辑指定的 Owner 用户 */
export const updateOwners = (userIds: (string | number)[] = []) => {
  return fetchPost(`/user/owner/applications/owner/update/user`, { userIds });
};

/** 编辑 指定 Owner toggle 的状态 (true/false) */
export const updateOwnerToggleStatus = (checked: boolean) => {
  return fetchGet(`/user/owner/applications/update/owner/${checked}`);
};

/** 获取 指定 Owner toggle 的状态  */
export const getOwnerToggleStatus = () => {
  return fetchGet(`/user/owner/applications/owner/status`);
};

/** 获取 登出提示 状态  */
export const getLogOutPrompt = () => {
  return fetchGet(`/user/sys/saveEditorContent`);
};

/** 设置 登出提示 状态 */
export const postLogOutPrompt = (flag: boolean) => {
  return fetchPost(`/user/sys/saveEditorContent/${flag}`);
};
/** 获取 行级锁是否开启 **/
export const getRowLock = () => {
  return fetchGet("/user/sys/forUpdate");
};
/** 设置 行级锁开启状态 **/
export const postRowLock = (flag: boolean) => {
  return fetchPost(`/user/sys/forUpdate/${flag} `);
};
/** 获取 导入数量 **/
export const getImportNumber = () => {
  return fetchGet("/user/sys/queue/import");
};
/** 获取 导出数量 **/
export const getExportNumber = () => {
  return fetchGet("/user/sys/queue/export");
};
/** 设置 导入数量 **/
export const postImportNumber = (count: number) => {
  return fetchPost(`/user/sys/queue/import/${count}`);
};
/** 设置 导入数量 **/
export const postExportNumber = (count: number) => {
  return fetchPost(`/user/sys/queue/export/${count}`);
};
/** 获取 登录目的开关 */
export const getLoginPurpose = (): Promise<boolean> => {
  return fetchGet(`/user/sys/loginPurpose`);
};
export const getDataMigration = async (): Promise<boolean> => {
  return await fetchGet(`/user/cloudquery/all/dataMigration`);
};
/** 设置 登录目的开关 */
export const postLoginPurpose = (flag: boolean) => {
  return fetchPost(`/user/sys/loginPurpose/${flag}`);
};
/** 设置 登录目的 */
export const postLoginPurposeContent = (param: { purpose: string }) => {
  return fetchPost(`/user/sys/loginPurpose`, param);
};
/** 设置 级联移除 **/
export const setCascadeDefault = (param: boolean) => {
  return fetchPost(`/user/sys/cascadeDefault/setting/${param}`);
};
/**
 * sql解析错误显示开关（实际上是权限检查开关）
 * 后端定义的是同一个接口
 * 获取数据调用接口不传参数，修改数据调用接口需要传参数
 * */

interface SqlMerrorIgnore {
  dataSourceType: string;
  flag: boolean;
}

/** 获取解析模式配置 */
export const getSqlMerrorIgnore = () => {
  return fetchGet(`/user/sys/strictmode`);
};

/** 保存解析模式配置 */
export const postSqlMerrorIgnore = (param: SqlMerrorIgnore[]) => {
  return fetchPost(`/user/sys/batchsavemode`, param);
};

/** IP 白名单开关 */
export const getIPWhiteSwitch = () => {
  return fetchGet(`/user/sys/openIPWhiteList`);
};

export const postIPWhiteSwitch = (openIPWhiteList: boolean) => {
  return fetchPost(`/user/sys/openIPWhiteList/${openIPWhiteList}`);
};
interface ICasConfig {
  casSwitch?: boolean;
  casServerLoginUrl?: string;
  casServerValidateUrl?: string;
  casServerLogoutUrl?: string;
  casCQServerName?: string;
  casUserNameSuffix?: string;
}

interface IAdConfig {
  adDirectSwitch?: boolean;
  adDirectLdapAddress?: string;
  adDirectSearchBase?: string;
  adDirectUsernameSuffix?: string;
}

interface ILdapConfig {
  adminSecret: string; // 管理员密码
  openLdapAdminName: string; // 管理员账号
  openLdapSwitch: boolean; // OpenLdap开关
  openLdapUrl: string; // OpenLdap登录地址
  searchScope: string; // 搜索范围
}

export interface IOauthConfig {
  oauth2Switch: boolean;
  clientId: string;
  clientSecret: string;
  userAuthorizationUri: string;
  accessTokenUri: string;
  oauth2CQServerUrl: string;
  resourceUserInfoUrl: string;
  oauthUserNameSuffix: string;
}
/** 保存oauth配置 */
export const putOauthConfig = (params: IOauthConfig) => {
  return fetchPut(`user/sys/oauth2`, params);
};

/** 获取oauth配置 */
export const getOauthConfig = () => {
  return fetchGet(`user/sys/oauth2`);
};
/** 获取cas配置 */
export const getCasConfig = () => {
  return fetchGet(`user/sys/cas `);
};
/** 保存cas配置 */
export const putCasConfig = (params: ICasConfig) => {
  return fetchPut(`user/sys/cas`, params);
};

/** 获取 ad 域配置 */
export const getAdConfig = () => {
  return fetchGet(`user/sys/adDirect`);
};
/** 更新 ad 域配置 */
export const putAdConfig = (params: IAdConfig) => {
  return fetchPut(`user/sys/adDirect`, params);
};

/** 获取 radius 配置 */
export const getRadiusConfig = () => {
  return fetchGet(`/user/sys/radius`);
};
/** 更新 radius 配置 */
export const putRadiusConfig = (params: IAdConfig) => {
  return fetchPut(`user/sys/radius`, params);
};
/** 获取 ukey 列表 */
export const getUkeyList = () => {
  return fetchGet(`/user/ukey/ukeys`);
};
interface UkeyItem {
  admin: boolean;
  deviceId: string;
  status: boolean;
  userId: string;
}
/** 删除 ukey  */
export const delUkey = (params: UkeyItem) =>
  fetchPost("/user/ukey/ukeys/delete", params);
/**  更新 ukey **/
export const updateUkey = (params: Partial<UkeyItem>) =>
  fetchPost("/user/ukey/ukeys/update", params);
/**  新增 ukey **/
export const addUkey = (params: Partial<UkeyItem>) =>
  fetchPost("/user/ukey/ukeys", params);

/** 获取流程-连接访问自动赋予select权限开关项 */
export const getFlowSelectPerm = () => {
  return fetchGet("/user/sys/connectionFlow");
};
/** 设置流程-连接访问自动赋予select权限开关项 */
export const postFlowSelectPerm = (flag: boolean) => {
  return fetchPost(`/user/sys/connectionFlow/${flag}`);
};

/** 权限看板开关 */
interface IDashSwitch {
  status: boolean;
}
export const dashboardSwitchPost = (params?: IDashSwitch) => {
  return fetchPost(`/user/sys/dashBoard/status`, params);
};

/** 获取OpenLdap 配置 */
export const getLdapConfig = () => {
  return fetchGet("/user/sys/openLdap");
};

/** 保存OpenLdap 配置 */
export const putLdapConfig = (params: ILdapConfig) => {
  return fetchPut("/user/sys/openLdap", params);
};

interface IExecLog {
  pagination: {
    page: number;
    pageSize: number;
  };
  sqlName?: string;
}

/**宽松模式 导出 */
export const exportStrictModeLog = (params: { sqlName?: string }) => {
  return fetchPost("/user/sys/export/strictmode", params);
};

/** 获取用户宽松模式下的执行记录 */
export const postExecLog = (params: IExecLog) => {
  return fetchPost("/user/history/getParsingFailedRecords", params);
};

/** 版本信息 */
export const getVersion = () => {
  return fetchGet("/user/versionInfo");
};

/**version信息 */
export const getVersionIsCommunity = () => {
  return fetchGet("/user/sys/version");
};

/**同步复核审批人 */
export interface IAmsApproverItem {
  key: string;
  value: string;
}
export const getAmsApprover = (params: {connectionId: number | string}): Promise<IAmsApproverItem> => {
  return fetchGet("/user/sys/query-sms-user", params);
};
export const getGroupAmsApprover = (params: {connectionId: number}): Promise<IAmsApproverItem> => {
  return fetchGet("/user/sys/query-group-sms-user", params);
};
export const getDataSourceAmsApprover = (datasourceType: string): Promise<IAmsApproverItem> => {
  return fetchGet(`/user/sys/query-datasource-sms-user/${datasourceType}`);
};

export type ISmsUserType = 'CONNECTION_MANAGER' | 'APPROVE_GROUP';
//修改复核审批人接口
export const updateAmsApprovers = (params: {connectionId: number,smsUserType: ISmsUserType}): Promise<any> => {
  return fetchGet("/user/sys/update-sms-user", params);
};

// 数据源类型同步复核审批人修改
interface IDatasourceAmsApprovers {
  datasourceType: string;
  smsUserType: ISmsUserType;
}
export const updateDataSourceAmsApprovers = (params: IDatasourceAmsApprovers): Promise<any> => {
  return fetchGet("/user/sys/update-datasource-sms-user", params);
};

export const updateGroupAmsApprovers = (params: {groupId: number,smsUserType: ISmsUserType}): Promise<any> => {
  return fetchGet("/user/sys/update-group-sms-user", params);
};

/** 授权管理信息 */
export const getAuthorizationMessage = () => {
  return fetchGet("/dms/versionInfo/authorMessage");
};

/** 获取cq3/6/9版本信息 */
export const getCq369 = () => {
  return fetchGet("/dms/versionInfo/productGrade");
};

interface IAppKeyParams {
  appKey: string;
  user: string;
  note?: string;
}

/** 系统管理-AppSecret- appKey list */
export const getAppKeyList = (params: any): Promise<any> => {
  return fetchPost("/gateway/cq/token/list", params);
};
/** 系统管理-AppSecret- 生成 */
export const appKeyGenerate = (params: IAppKeyParams): Promise<string> => {
  return fetchPut("/gateway/cq/token", params);
};
/** 系统管理-AppSecret- 重置 */
export const appKeyReset = (params: IAppKeyParams): Promise<string> => {
  return fetchPost("/gateway/cq/token", params);
};
/** 系统管理-AppSecret- 编辑 */
export const appKeyEdit = (params: {
  id: number;
  note: string;
}): Promise<string> => {
  return fetchPut(`/gateway/cq/token/${params.id}`, params);
};
/** 系统管理-AppSecret- 删除 */
export const appKeyDelete = (id: number): Promise<string> => {
  return fetchDelete(`/gateway/cq/token/${id}`);
};
interface ISysPasswrdPolicy {
  maxDay: string | number;
  noRepeatCount: string | number;
  expireWarningDay: string | number;
  maxFailureCount: string | number;
  lockoutMinute: string | number;

  passwordMax?: string | number;
  passwordMin?: string | number;
  containDigits?: boolean;
  containUpperLetters?: boolean;
  containLowerLetters?: boolean;
  containSymbols?: boolean;
  systemPasswordStrong?: boolean;
  defaultPassword?: string | number;
}
/** 密码域配置 获取密码策略 */
export const getSysPasswordPolicy = () => {
  return fetchGet("/user/sys/passwordPolicy");
};
/** 密码域配置 修改密码策略 */
export const updateSysPasswordPolicy = (
  params: ISysPasswrdPolicy
): Promise<string> => {
  return fetchPut("/user/sys/passwordPolicy", params);
};

/**
 * 系统设置 水印
 */
export const setWatermarkSetting = (params: any): Promise<any> => {
  return fetchPost("/user/sys/watermark/setting", params);
};

export interface IWatermarkEffectParams {
  fontSize: string, // 字号
  horizontalGap: string, // 水平间距
  verticalGap: string, // 垂直间距
  colorValue: string, // 颜色
  transparency: string, // 透明度
}

// 系统设置设置水印效果
export const setWatermarkEffectSetting = (params: IWatermarkEffectParams): Promise<any> => {
  return fetchPost("/user/sys/watermark/watermarkEffectUpdate", params);
};

// 获取 水印效果
export const getWatermarkEffectSetting = (): Promise<any> => {
  return fetchGet("/user/sys/watermark/watermarkEffect");
};

/**
 * 系统设置 获取水印
 */
export const getWatermarkSetting = (type: string) => {
  return fetchGet(`/user/sys/watermark/setting?type=${type}`);
};

/* 系统设置 用户登录限制 IP白名单*/
export const getUserLoginIpList = () => {
  return fetchGet(`/user/login/ip/list`);
};

export const postUserLoginIpList = (params: any) => {
  return fetchPost(`/user/login/ip/list`, params);
};

/* 系统设置  文件读取/上传 限制*/
export const getUserSysFile = () => {
  return fetchGet(`/user/sys/file/setting`);
};

export const postUserSysFile = (params: {
  readSize: string | number;
  uploadSize: string | number;
}) => {
  return fetchPost(`/user/sys/file/setting`, params);
};

/* 系统设置  获取帐号锁定 期限*/
export const getUserSysLogin = () => {
  return fetchGet(`/user/sys/login/term`);
};


export const postUserSysLogin = (days: number | string) => {
  return fetchPost(`/user/sys/login/term/${days}`);
};

/* 系统设置  获取结果集导出加密*/
export const getResultEnc = () => {
  return fetchGet(`/user/sys/export/encrypt`);
};

export const postResultEnc = (isEnc:boolean) => {
  return fetchPost(`/user/sys/encrypt/${isEnc}`);
};

/* 系统设置  获取批量执行任务数*/
export const getBatchExec = () => {
  return fetchGet(`/user/sys/batch/exec`);
};

export const postBatchExec = (num: number | string | null) => {
  return fetchPost(`/user/sys/batch/exec/${num}`);
};

/** 系统设置  获取是否可以多设备登录 */
export const getMultipleDeviceLogin = () => {
  return fetchGet(`/user/sys/multipleDeviceLogin/setting`);
};

/** 系统设置  设置是否可以多设备登录 */
export const postMultipleDeviceLogin = (mul: boolean) => {
  return fetchPost(`/user/sys/multipleDeviceLogin/setting/${mul}`);
};

/** 系统设置  获取是否全局强制otp登录 */
export const getOtpForceLogin = () => {
  return fetchGet(`/user/sys/globalOtpLogin/setting`);
};

/** 系统设置  设置是否全局强制otp登录 */
export const postOtpForceLogin = (val: boolean) => {
  return fetchPost(`/user/sys/globalOtpLogin/setting/${val}`);
}

/** 系统设置  获取是否全局强制otp、短信登录 */
export const getForceLogin = () => {
  return fetchGet(`/user/loginSetting/getForcedBinding`);
};

/** 系统设置  获取系统用户登录保持时间 */
export const getRetentionTimeType = () => {
  return fetchGet(`/user/sys/session`);
}

/** 系统设置 基础设置 获取用户级联移除设置*/
export const getCascadeDefault = () => {
  return fetchGet(`/user/sys/cascadeDefault/setting`);
}

export interface IResultSetTipOption {
  key: string;
  name: string;
  selected: boolean;
}
//获得结果集入口选项配置
export const getResultSetTipOption = (): Promise<IResultSetTipOption[]> => {
  return fetchGet(`/user/sys/resultSet/tipOption`);
}
//修改结果集入口选项配置
export const setResultSetTipOption = (params: {key: string; selected: boolean}[]): Promise<IResultSetTipOption[]> => {
  return fetchPut(`/user/sys/resultSet/tipOption`, params);
}
//获得数据备份有效期的接口
export const getBackupValidperiod = (): Promise<any> => {
  return fetchGet(`/user/sys/backup/validperiod`);
}
//修改数据备份有效期的接口
export const updateBackupValidperiod = (params: {days: string}): Promise<any> => {
  return fetchPut(`/user/sys/backup/validperiod`,params);
}
//连接失败冻结次数
export const getConnectionFailedCount = (): Promise<any> => {
  return fetchGet(`/user/sys/connectionFailedCount/setting`);
}
export const updateConnectionFailedCount = (count: number): Promise<any> => {
  return fetchPost(`/user/sys/connectionFailedCount/setting/${count}`);
}

// 同步数据字典任务遇到错误是否继续
export const getDictExecuteSetting = (): Promise<any> => {
  return fetchGet(`/user/sys/dict-execute-setting`);
}
export const updateDictExecuteSetting = (flag: boolean): Promise<any> => {
  return fetchPost(`/user/sys/dict-execute-setting/${flag}`);
}

/** 系统设置  设置是否全局强制otp、短信登录 */
interface IParam {
  forcePhoneBing: boolean,
  forceOtpBing: boolean
}
export const postForceLogin = (param: IParam) => {
  return fetchPost(`/user/loginSetting/saveForcedBinding`, param);
}

/** 系统设置  设置 系统用户登录保持时间设置 */
export const saveRetentionTimeType = (param: {[key: string]: any}) => {
  return fetchPost(`/user/sys/session`, param);
}

/** 系统设置  获取 系统用户登录设置 */
export const getSettingType = () => {
  return fetchGet(`/user/loginSetting/getSettingType`);
}
/** 系统设置  设置 系统用户登录设置 */
export const saveSettingType = (param: {[key: string]: string}) => {
  return fetchPost(`/user/loginSetting/saveSettingType`, param);
}

/** 系统设置  获取是否需要强制进行 手机号绑定、otp绑定 */
export const getForcedBindingByUser = () => {
  return fetchGet(`/user/loginSetting/getForcedBindingByUser`);
};


/* 系统设置  设置资源搜索页是否展示所有资源*/
export const postApplyResourceSearchSetting = (flag: boolean) => {
  return fetchPost(`/user/sys/applyResourceSearch/setting/${flag}`);
};

/* 系统设置  获取资源搜索页是否展示所有资源*/
export const getApplyResourceSearchSetting = () => {
  return fetchGet(`/user/sys/applyResourceSearch/setting`);
};

/* 系统设置  是否自动下载*/
export const getApplyExportAutoDownloadSetting = () => {
  return fetchGet(`/user/sys/exportAutoDownload/setting`);
};

/* 系统设置  设置是否自动下载*/
export const postExportAutoDownloadSetting = (flag: boolean) => {
  return fetchPost(`/user/sys/exportAutoDownload/setting/${flag}`);
};
/**
 * 系统设置-手机短信设置 查看自定义消息网关接口
 */
export const getSms = () => {
  return fetchGet(`/user/sys/sms`);
};

/**
 * 删除 告警配置
*/
export const delSms = (id: string | number) => {
  return fetchDelete(`user/sys/sms?id=${id}`);
};

/**
 * 短信网关-告警配置
 * 服务参数可用的系统变量名，这个接口是让用户在填网关的时候选择业务，可以用的参数
*/
export const getParam = (businessType: string): Promise<{
  description: string;
  name: string;
}[]> => {
  return fetchGet(`/user/sys/param?businessType=${businessType}`);
};

/**
 * 获取系统用户登录设置
 */
export const getUserLoginSetting = (): Promise<{
  value: boolean;
}> => {
  return fetchGet(`/user/sys/forceLogin`);
};
// 设置用户登录方式
export const putUserLoginSetting = (value: string) => {
  return fetchPut(`/user/sys/forceLogin?value=${value}`);
};

/**
 * 系统设置-手机短信设置 更改消息接口网关 url：/user/sys/sms
 */
export interface StringMap {
  [key: string]: string;
}
export interface IParmas {
  // 网关地址
  baseUrl: string;
  // 网关请求方式 GET POST
  method: string;
  // 请求头字段
  headers?: StringMap;
  // url参数字段
  urlParams: StringMap;
  // body数据，只有请求方式为post时生效
  body?: string;
  phone?: string;
  [p: string]: any;
}

/**
 * 更改消息接口网关
 */
export const updateSms = (params: IParmas) => {
  return fetchPost(`/user/sys/sms`, params);
};
/**
 * 系统设置-手机短信设置 发送验证短信，测试短信网关
 */
export const smsTestPhone = (params: IParmas) => {
  return fetchPost(`/user/sys/sms/test`, params);
};

// json转map
export const jsonToMap = (jsonData: string) => {
  // 解析 JSON 数据为 JavaScript 对象
  const parsedData = JSON.parse(jsonData);

  // 创建空的 Map 实例
  const resultMap: Map<string, string> = new Map();

  // 将属性添加到 Map 中
  for (const key in parsedData) {
    if (Object.hasOwn(parsedData, key)) {
      const value = parsedData[key];
      resultMap.set(key, String(value));
    }
  }
  return resultMap;
};
// 二维数组转map
export const twoArrToMap = (arr: string[][] | undefined) => {
  // 创建空的 Map 实例
  const resultMap: Map<string, string> = new Map();

  // 将二维数组转换为 Map
  arr?.forEach(([key, value]) => {
    resultMap.set(key, value);
  });
  return resultMap;
};
// map 转 StringMap {[key: string]: string}
export const convertMapToStringMap = (map: Map<string, string>): StringMap => {
  const stringMap: StringMap = {};
  for (const [key, value] of map) {
    stringMap[key] = value;
  }
  return stringMap;
};

export const addParamToJsonData = (
  map: Map<string, string>,
  jsonData: { [x: string]: string }
) => {
  for (let key in jsonData) {
    if (jsonData.hasOwnProperty(key)) {
      map.set(key, jsonData[key]);
    }
  }
  return map;
};

// body
export const validateJson = (
  _: any,
  value: any,
  cb: any,
  isNotEmpty: boolean
) => {
  if (isNotEmpty || (!isNotEmpty && value)) {
    try {
      JSON.parse(value);
      cb();
    } catch (error) {
      cb(i18n.t("pleaseEnterCorrectJsonFormat"));
    }
  }
  cb();
};

// phone
export const validatePhone = (_: any, value: any) => {
  const phoneRegex = /^1\d{10}$/; // 正则表达式，用于匹配中国大陆手机号码 
  if (value && !phoneRegex.test(value)) {
    return Promise.reject(i18n.t("validPhoneNumber"));
  }
  return Promise.resolve();
};

// json对象转json字符串
export const getJsonStr = (jsonData: { [x: string]: string }) => {
  let urlParamsStr: string = "";
  const keys = Object.keys(jsonData);
  for (let key in jsonData) {
    if (jsonData.hasOwnProperty(key)) {
      urlParamsStr += '"' + key + '":' + jsonData[key];
    }
    if (key !== keys[keys.length - 1]) {
      // 不是最后一个json对象
      if (jsonData.hasOwnProperty(key)) {
        urlParamsStr += ",";
      }
    }
  }
  urlParamsStr = "{" + urlParamsStr + "}";
  return urlParamsStr;
};
export interface IAccessProps {
  id?: number;
  ipAddr: string;
  type: number; //表单中的访问策略， 0为白名单， 1为黑名单
  comment?: string;
  updatedAt?: string;
}

export interface IFilterAccessParams {
  condition?: number | undefined;
}

export interface ConListType {
  pageNo?: number,
  pageSize?: number,
  type?: number,
  ipAddr?: string,
  comment?:string
}

/* 获取系统访问限制信息列表 */
export const getAccessList = (
  condition: number | undefined
): Promise<IAccessProps[]> => {
  return fetchGet(`/user/sys/access/restriction/list?condition=${condition}`);
};

/* 根据条件获取系统访问限制信息列表 */

export interface AccessRuleItem {
  comment: string;
  id: number;
  ipAddr: string;
  type: number;
  updatedAt: string;
  userIds: number[];
  userInfo: string
}

export interface AccessRuleRes {
  totalPage: number;
  pageNo: number;
  pageSize: number;
  totalItem: number;
  data: AccessRuleItem[];
}

export const getConList = (params:ConListType): Promise<AccessRuleRes> => {
  return fetchPost(`/user/sys/access/restriction/list`,params)
}

/* 新增/修改 系统访问限制信息时，校验是否有在线用户被影响 */
export const checkAccess = (params: IAccessProps) => {
  return fetchPost(`/user/sys/access/restriction/update/check`, params);
};

/* 新增/修改 系统访问限制信息 */
export const updateAccess = (params: IAccessProps) => {
  return fetchPost(`/user/sys/access/restriction/update`, params);
};

/* 删除 系统访问限制信息时，校验是否有在线用户被影响 */
export const checkDeleteAccess = (id: number) => {
  return fetchDelete(`/user/sys/access/restriction/check/${id}`);
};

/* 删除访问资源 */
export const deleteAccess = (id: number) => {
  return fetchDelete(`/user/sys/access/restriction/${id}`);
};

/* 批量删除访问资源 */
export const batchDeleteAccessRule = (ids: number[]) => {
  return fetchDelete(`/user/sys/access/restriction/batch`,{ids});
};

/* 批量导出访问资源 */
export const batchExportAccessRule = (ids: number[]) => {
  return fetchPost(`/export/export/access/restriction`,{ids});
};

/** 获取导入结果 */
export const getBatchImportAccessRuleResult = (skip: boolean,params: any) => {
  return fetchPostFormData(`/user/sys/access/restriction/import/${skip}`, params)
}

/* 访问系统之前 校验接口 */
export const checkIp = () => {
  return fetchGet(`/user/sys/check/access/ip`);
};

/* 数据操作 短信复核-获取验证码 */
interface checkSmsParams {
  connectionId: number | string;
  queryTabKey: string;
  [p: string]: any;
}
export const getCheckSms = (params: checkSmsParams) => {
  return fetchGet(`/user/sys/check/sms`, params);
};

/* 判断传sql时是否需要编码 */
export const ifBase64Encode = () => {
  return fetchGet(`/user/sys/ifBase64Encode`);
};

/** 获取配置 */
export const getAuditLogConfig = (params: {groupKey: string}) => {
  return fetchPost(`/user/base/configuration/list`, params);
};

// 数据库主机连接: 获取 hostSSH 配置
export const getHostSSH = () => {
  return fetchGet(`user/base/configuration/hostSSH`);
};

/** 获取 syslog 配置 */
export const getSyslogConfig = () => {
  return fetchGet(`/user/log/expose/syslog`);
};

/** 保存 syslog 配置 */
interface IParams {
  host: string,
  port: string,
  protocol: string,
  enable: boolean,
  level: string
}
export const postSyslogConfig = (params: IParams) => {
  return fetchPost(`/user/log/expose/syslog`, params);
};



/** 获取 kafka 配置 */
export const getKafkaConfig = () => {
  return fetchGet(`/user/log/expose/kafka`);
};

/** 保存 kafka 配置 */
interface IParams {
  host: string,
  topic: string,
  enable: boolean,
  clientId: string
}
export const postKafkaConfig = (params: IParams) => {
  return fetchPost(`/user/log/expose/kafka`, params);
};

/** kafka配置 是否显示 */
export const getKafkaShow = () => {
  return fetchGet(`/user/log/expose/kafka/show`);
};

/** 获取 采样行数、命中率 */
export const getSampleCheck = () => {
  return fetchGet(`user/sys/setting/sample/check`);
};

/** 设置 采样行数、命中率 */
export const postSampleCheck = (sampleCount: number | string, sampleRate: number | string) => {
  return fetchPost(`user/sys/setting/sample/${sampleCount}/${sampleRate}`);
};

/** 设置 文件下载次数 */
export const postDownloadLimit = (param: {limit: number}) => {
  return fetchPost(`/user/sys/file/downloadLimit`, param);
};

interface IGlobalEffectTime {
  globalEffectTime: string
}

/** 设置 双因素认证生效时间 */
export const postGlobalEffectTime = (params:IGlobalEffectTime) => {
  return fetchPost(`user/sys/auth/global/time`, params);
};

/** 获取 双因素认证生效时间初始值 */
export const getGlobalEffectTime = () => {
  return fetchGet(`user/sys/setting?settingName=TWO_FACTOR_GLOBAL_EFFECT_TIME`);
};

/** 获取 获取文件下载次数 */
export const getDownloadLimit = () => {
  return fetchGet(`/user/sys/file/downloadLimit`);
};

/** 获取 工具权限配置 */
export const getToolPermissionSetting = () => {
  return fetchGet(`/user/sys/tool-permission-setting-selected`);
};

/** 设置 工具权限配置 */
export const updateToolPermissionSetting = (params: {toolPermissionSettings: {id: number, selected: boolean}[]}) => {
  return fetchPost(`/user/sys/update-tool-permission-setting`, params);
};

/** 获取 所有工具权限配置 */
export const getAllToolPermissionSetting = (tab: 'sdtMenu' | 'resultSetOperation' | 'ALL') => {
  return fetchGet(`user/sys/tool-permission-setting/${tab}`);
};

/**
 * 获取所有数据源
 * @return string[]
 */
export const getAllDatasource = () => {
  return fetchGet(`/user/sys/getAllDatasource`)
}

/**
 * 获取datasource数据源的所有过滤资源 
 * @return string[]
*/
export const getFilterResource = (datasource: string) => {
  return fetchGet(`/user/sys/getFilterResource/setting/${datasource}`)
}

/**
 * 设置datasource数据源的过滤资源
 * @param any[]
 * @return 
*/
export const postFilterResource = (datasource: string, params: any[]) => {
  return fetchPost(`/user/sys/applyFilterResource/setting/${datasource}`, params);
};

/**
 * 上传新证书
 * @param any[]
 * @return 
*/
export const uploadLicense = (data: any) => {
  return fetchPostFormData(`/license/upload`, data)
}

/**
 * 删除证书
*/
export const deleteLicense = (fileName: string) => {
  return fetchGet(`/license/delete?fileName=${fileName}`)
}

/**
 * 更新证书
*/
export const updateLicense = () => {
  return fetchGet(`/license/one/update`)
}

/**
 * 获取证书列表
*/
export const getLicenseList = () => {
  return fetchGet(`/license/list`)
}

/**
 * 更新证书-查看任务状态
*/
export const getLicenseStatus = (): Promise<{
  desc?: string; // 执行阶段名称
  status?: number; // 执行进度 0=> 初始状态,证书未进行更新
  progress?: number; // 执行进度百分比
  execInfo?: string; // 执行信息
  isExecuting?: boolean; // 是否 是正在更新状态
  statusStr?: string; // 证书状态:初始化,更新中,更新成功, 更新失败
}> => {
  return fetchGet(`/license/status`)
}

// 获取更新日志信息的语雀地址
export const getChangelogUrl = () => {
  return fetchGet(`/user/versionInfo/changelog`)
}
interface DebugUrlParams {
  publicDebugUrl?: string,
  privateDebugUrl?: string
}

/**
 * 设置 调试地址配置
 * param: DebugUrlParams
 * @return 
*/
export const setDebugUrl = (param: DebugUrlParams) => {
  return fetchPost(`/user/sys/setting/setDebugUrl`, param);
};

/**
 * 获取 调试地址配置
 * @return 
*/
export const getDebugUrl = (): Promise<DebugUrlParams> => {
  return fetchGet(`/user/sys/setting/getDebugUrl`);
};

export type ParameterIUnit = 'day' | 'hour' | 'forever' | 'custom';

export interface IValidTimeItem {
  id: number;
  timeName: string;
  timeValue: number;
  timeValueUnit: ParameterIUnit;
  timeStatus?: boolean;
  timeOrder?: number;
  timeEdit?: boolean;
}
/**
 * 系统设置-权限 - 所有有效时间配置
 */

export const getValidaTimeList = (): Promise<IValidTimeItem[]> => {
  return fetchGet(`/user/sys/system/valid-time/list`);
};

/**
 * 系统设置-权限 -添加有效时间配置
 */

export const addValidaTime = (params: IValidTimeItem): Promise<boolean> => {
  return fetchPost(`/user/sys/system/valid-time/add`, params);
};

/**
 * 系统设置-权限 -更新有效时间配置
 */

export const updateValidTime = (params: IValidTimeItem): Promise<boolean> => {
  return fetchPost(`/user/sys/system/valid-time/update`, params);
};

/**
 * 系统设置-权限 -删除有效时间配置
 */

export const deleteValidaTime = (params: {id: number}): Promise<boolean> => {
  return fetchPost(`/user/sys/system/valid-time/delete`, params);
};

/**
 * 系统设置-权限 -删除有效时间配置
 */

export const moveValidTimeItem = (params: {sourceId: number; targetId: number}): Promise<boolean> => {
  return fetchPost(`/user/sys/system/valid-time/move`, params);
};

/**
 * 系统设置-权限 -获取所有开启的有效时间配置
 */

export const getEnabledValidTimeList = (): Promise<IValidTimeItem[]> => {
  return fetchGet(`/user/sys/system/valid-time/open-list`);
};

export interface ApifoxModel {
    advanced?: string;
    api_key?: string;
    max_token?: number;
    model?: string;
    model_ali?: string;
    request_type?: string;
    temperature?: number;
    top_p?: number;
    url?: string;
    [property: string]: any;
}

/**
 * 系统设置-大模型 - 设置大模型
 */
export const setLLMConfig = (params: {
    advanced?: string;
    api_key?: string;
    max_token?: number;
    model?: string;
    model_ali?: string;
    request_type?: string;
    temperature?: number;
    top_p?: number;
    url?: string;
}) => {
  return fetchPost(`/user/classification/forward/api/llm_config`, params);
}
/**
 * 系统设置-大模型 - 测试大模型
 */
export const testLLM = (params: {
    advanced?: string;
    api_key?: string;
    max_token?: number;
    model?: string;
    model_ali?: string;
    request_type?: string;
    temperature?: number;
    top_p?: number;
    url?: string;
}) => {
  return fetchPost(`/user/classification/forward/api/llm_test`, params);
}

/**
 * 系统设置-大模型 - 获取请求类型
 */
export const getRequestType = (params: {
    key:string
}) => {
  return fetchPost(`/user/classification/forward/api/query_storage`, params);
}

/**
 * 系统设置-大模型 - 获取模型类型
 */
export const getModelType = (params: {
    key:string
}) => {
  return fetchPost(`/user/classification/forward/api/query_storage`, params);
}

/**
 * 系统设置-大模型 - 获取大模型设置
 */
export const getLLMConfig = () => {
  return fetchGet(`/user/classification/forward/api/get_llm_config`);
}