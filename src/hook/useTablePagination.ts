import { useState } from "react";
export const useTablePagination = (initialValue: {
  current: number;
  pageSize: number;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
},searchParams:any,setSearchParams: (params: any) => void) => {
  const [pagination, setPagination] = useState(initialValue);
  
  const handleChange = (pagination: any) => {
    setPagination({
      ...pagination,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
    setSearchParams({
      ...searchParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    })
  }
  return {
    pagination,
    handleChange
  }
}

